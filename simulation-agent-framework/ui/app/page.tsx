"use client";

import { CopilotSidebar } from "@copilotkit/react-ui";
import { <PERSON><PERSON>, Zap, Layers, Settings, Play, BarChart3 } from "lucide-react";

export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <SimulationMainContent />
      <CopilotSidebar
        defaultOpen={true}
        labels={{
          title: "Simulation Assistant",
          initial: "Hello! I'm your AI-powered simulation assistant. I can help you with geometry processing, physics simulation, and results analysis. What would you like to simulate today?",
        }}
      />
    </main>
  );
}

function SimulationMainContent() {
  return (
    <div className="h-screen w-screen flex flex-col">
      {/* Header */}
      <header className="bg-black/20 backdrop-blur-sm border-b border-white/10 p-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-gradient-to-r from-blue-500 to-cyan-500 p-3 rounded-xl">
              <Cpu className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">Simulation Agent Framework</h1>
              <p className="text-blue-200">AI-Powered Engineering Simulation Platform</p>
            </div>
          </div>
          <div className="flex items-center space-x-2 text-sm text-blue-200">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>NVIDIA Technology Stack</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="max-w-6xl mx-auto text-center">
          {/* Hero Section */}
          <div className="mb-12">
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6 leading-tight">
              Next-Generation
              <span className="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent"> AI Simulation</span>
            </h2>
            <p className="text-xl text-blue-200 mb-8 max-w-3xl mx-auto">
              Automated end-to-end engineering simulation powered by NVIDIA Omniverse, PhysicsNeMo, and intelligent multi-agent orchestration.
            </p>
          </div>

          {/* Technology Stack */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="bg-gradient-to-r from-green-500 to-emerald-500 p-3 rounded-xl w-fit mx-auto mb-4">
                <Layers className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">NVIDIA Omniverse</h3>
              <p className="text-blue-200">3D world representation and collaborative environment using Universal Scene Description (USD)</p>
            </div>

            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="bg-gradient-to-r from-purple-500 to-violet-500 p-3 rounded-xl w-fit mx-auto mb-4">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">PhysicsNeMo</h3>
              <p className="text-blue-200">Physics-informed neural networks for scalable, GPU-optimized scientific machine learning</p>
            </div>

            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6">
              <div className="bg-gradient-to-r from-orange-500 to-red-500 p-3 rounded-xl w-fit mx-auto mb-4">
                <Settings className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Multi-Agent System</h3>
              <p className="text-blue-200">Intelligent orchestration with specialized PrepAgent, SolverAgent, and PostAgent</p>
            </div>
          </div>

          {/* Workflow Steps */}
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 mb-8">
            <h3 className="text-2xl font-semibold text-white mb-6">Automated Workflow</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex flex-col items-center">
                <div className="bg-blue-500 p-4 rounded-full mb-3">
                  <Play className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-medium text-white mb-2">1. Domain Preparation</h4>
                <p className="text-blue-200 text-sm">Geometry generation, cleanup, meshing, and physics configuration</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="bg-purple-500 p-4 rounded-full mb-3">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-medium text-white mb-2">2. Physics Simulation</h4>
                <p className="text-blue-200 text-sm">PhysicsNeMo execution with convergence monitoring</p>
              </div>
              <div className="flex flex-col items-center">
                <div className="bg-green-500 p-4 rounded-full mb-3">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                <h4 className="text-lg font-medium text-white mb-2">3. Post-Processing</h4>
                <p className="text-blue-200 text-sm">Results analysis and professional visualization</p>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center">
            <p className="text-lg text-blue-200 mb-4">
              Ready to start your simulation? Use the AI assistant to describe your engineering problem.
            </p>
            <div className="flex items-center justify-center space-x-2 text-sm text-green-400">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span>AI Assistant Ready</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
