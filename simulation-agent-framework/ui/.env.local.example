# Simulation Agent Framework UI - Environment Configuration

# Google AI Configuration (Primary)
GOOGLE_API_KEY=your_google_api_key_here

# OpenAI Configuration (Backup)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini

# LangGraph Agent Configuration
LANGGRAPH_AGENT_URL=http://localhost:8124

# Next.js Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_AGENT_URL=http://localhost:8124

# Development Settings
NODE_ENV=development
