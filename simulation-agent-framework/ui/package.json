{"name": "ai-researcher-demo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3000", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@agentwire/client": "0.0.26", "@agentwire/core": "0.0.26", "@agentwire/encoder": "0.0.26", "@agentwire/proto": "0.0.26", "rxjs": "7.8.1", "@copilotkit/react-core": "0.0.0-mme-awp-20250407201240", "@copilotkit/react-ui": "0.0.0-mme-awp-20250407201240", "@copilotkit/runtime": "0.0.0-mme-awp-20250407201240", "@copilotkit/runtime-client-gql": "0.0.0-mme-awp-20250407201240", "@copilotkit/shared": "0.0.0-mme-awp-20250407201240", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.1.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.3.31", "lucide-react": "^0.436.0", "next": "15.1.0", "openai": "^4.85.1", "react": "19.0.0", "react-dom": "19.0.0", "react-markdown": "^9.0.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^22.0.0", "@types/react": "19.0.1", "@types/react-dom": "19.0.2", "eslint": "^9.0.0", "eslint-config-next": "15.1.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "pnpm": {"overrides": {"@types/react": "18.2.38", "@types/react-dom": "18.2.15", "react": "18.2.0", "react-dom": "18.2.0", "eslint": "9.0.0", "@typescript-eslint/parser": "8.18.0", "@typescript-eslint/eslint-plugin": "8.18.0"}}}