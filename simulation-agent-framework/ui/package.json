{"name": "simulation-agent-framework-ui", "version": "1.0.0", "private": true, "description": "AI-Powered Engineering Simulation Platform - Frontend Interface", "scripts": {"preinstall": "npm run install:agent", "install:agent": "cd ../agent && npm install", "dev": "concurrently \"npm run dev:ui\" \"npm run dev:agent\" --names ui,agent --prefix-colors blue,green --kill-others", "dev:ui": "next dev --port 3000", "dev:agent": "cd ../agent && npx @langchain/langgraph-cli dev --port 8124 --no-browser", "dev:studio": "cd ../agent && npx @langchain/langgraph-cli dev --port 8124", "build": "next build && npm run build:agent", "build:agent": "cd ../agent && npm run build || echo 'Agent build skipped'", "start": "next start", "lint": "next lint", "clean": "rm -rf .next node_modules ../agent/node_modules", "reinstall": "npm run clean && npm install"}, "dependencies": {"@copilotkit/react-core": "^1.8.0", "@copilotkit/react-ui": "^1.8.0", "@copilotkit/runtime": "^1.8.0", "@copilotkit/runtime-client-gql": "^1.8.0", "@langchain/core": "^0.3.14", "@langchain/langgraph": "^0.4.9", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.3.31", "lucide-react": "^0.543.0", "next": "15.5.2", "react": "19.0.0", "react-dom": "19.0.0", "react-markdown": "^9.0.1", "recharts": "^3.2.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "zod": "^3.24.4"}, "devDependencies": {"@langchain/langgraph-cli": "0.0.40", "@types/node": "^22.0.0", "@types/react": "19.0.1", "@types/react-dom": "19.0.2", "@types/uuid": "^10.0.0", "concurrently": "^9.1.2", "eslint": "^9.0.0", "eslint-config-next": "15.5.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "keywords": ["simulation", "engineering", "ai-agent", "langgraph", "copilotkit", "nvidia", "physics"], "author": "Simulation Agent Framework Team", "license": "MIT"}