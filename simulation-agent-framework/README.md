# Simulation Agent Framework

An AI-powered engineering simulation platform implementing the architectural blueprint from agent-design.md. This framework follows the NVIDIA technology stack with hierarchical orchestrator-worker architecture using Google GenAI as the main LLM provider.

## 🏗️ Architecture Overview

The platform implements a sophisticated multi-agent system based on NVIDIA's technology stack:

- **🎯 OrchestratorAgent**: Main coordinator managing simulation workflows
- **🔧 PrepAgent**: Specialized in geometry processing, meshing, and domain preparation
- **⚡ SolverAgent**: Manages PhysicsNeMo execution and convergence monitoring
- **📊 PostAgent**: Handles results analysis and professional visualization

## 🛠️ Technology Stack

- **3D World**: NVIDIA Omniverse (Universal Scene Description)
- **Physics Engine**: NVIDIA PhysicsNeMo (Physics-informed neural networks)
- **AI Framework**: Google GenAI (Gemini) with LangGraph orchestration
- **Frontend**: Next.js with CopilotKit integration
- **Agent Framework**: LangGraph multi-agent system

## 📁 Project Structure

```text
simulation-agent-framework/
├── agent/                    # LangGraph-based multi-agent system
│   ├── src/
│   │   ├── agent.ts         # Main agent orchestration
│   │   ├── types.ts         # Type definitions
│   │   ├── agents/          # Specialized agent implementations
│   │   └── tools/           # Simulation-specific tools
│   ├── package.json
│   └── langgraph.json
├── ui/                      # Next.js frontend with CopilotKit
│   ├── app/
│   │   ├── page.tsx         # Main simulation interface
│   │   ├── layout.tsx       # App layout with CopilotKit
│   │   └── api/copilotkit/  # API integration
│   └── package.json
└── agent-py/               # Legacy Python backend (deprecated)
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18.18.0 or newer
- npm or pnpm (for package management)
- Google AI API key (for Gemini)

### 1. Clone and Install

```bash
git clone <repository-url>
cd simulation-agent-framework
```

> Note: We recommend using a virtual environment for your project to keep your packages contained. See <a href="https://docs.python.org/3/library/venv.html" target="_blank">venv</a>.

```sh
cd agent-py
pip install -r requirements.txt
```

### 2. Set up your API Key

Before running the code, you need to set your OpenAI API key as an environment variable:

**macOS/Linux:**
```sh
export OPENAI_API_KEY="your_openai_api_key"
```

**Windows:**
```sh
setx OPENAI_API_KEY "your_openai_api_key"
```

> Note: This example (simple_workflow.py) uses `gpt-4o-mini` by default, but you can replace it with any other model supported by AG2 by modifying the configuration in the code.

### 3. Start the Backend Server

The command below assumes that you are already inside the `agent-py` directory. If not please `cd` into the directory before running the command.

```sh
uvicorn simple_workflow:app --port 8008 --reload
```

> The command above starts the simple agent chat workflow. You can explore other workflows available in the `agent-py` directory, try them out, or even create your own. Each workflow file includes the command to run it at the bottom.

The backend server will start at http://localhost:8008.

## Frontend Setup

### 1. Install Frontend Dependencies

Open a new terminal session and run the below command

```sh
cd ui
pnpm i
```

### 2. Start the Frontend Application

The command below assumes that you are already inside the `ui` directory. If not please `cd` into the directory before running the command.

```sh
pnpm run dev
```

The frontend application will start at http://localhost:3000.

After starting the frontend, please allow a few moments for the Next.js application to compile fully. Once compilation is complete, you can interact with the chat window to communicate with the AG2 agent.

## Additional Resources

- <a href="https://docs.ag2.ai/latest/" target="_blank">AG2 Documentation</a>
- <a href="https://docs.copilotkit.ai/" target="_blank">CopilotKit Documentation</a>
