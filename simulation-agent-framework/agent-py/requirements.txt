# Simulation Agent Framework Dependencies
# Core LangGraph and AI dependencies
@langchain/core>=0.3.14
@langchain/langgraph>=0.4.9
@langchain/google-genai>=0.1.4
@langchain/openai>=0.3.14

# CopilotKit integration
@copilotkit/sdk-js>=1.3.7

# Simulation and scientific computing
numpy>=1.24.0
scipy>=1.10.0
matplotlib>=3.7.0

# 3D geometry and mesh processing
trimesh>=4.0.0
meshio>=5.3.0
pyvista>=0.43.0

# Physics simulation (mock implementations)
# Note: In production, these would be actual NVIDIA PhysicsNeMo dependencies
sympy>=1.12.0
yaml>=6.0.0

# Utility dependencies
uuid>=1.30
zod>=3.24.4
fastapi>=0.104.0
uvicorn>=0.24.0