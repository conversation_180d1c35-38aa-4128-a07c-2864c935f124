{"name": "simulation-agent-framework", "version": "1.0.0", "description": "AI-Powered Engineering Simulation Platform - Multi-agent framework following NVIDIA technology stack", "main": "src/agent.ts", "type": "module", "scripts": {"dev": "npx @langchain/langgraph-cli dev --port 8124 --no-browser", "dev:studio": "npx @langchain/langgraph-cli dev --port 8124", "build": "tsc", "start": "node dist/agent.js", "lint": "eslint src --ext .ts", "test": "jest", "clean": "rm -rf dist node_modules", "reinstall": "npm run clean && npm install"}, "keywords": ["simulation", "engineering", "ai-agent", "langgraph", "copilotkit", "nvidia", "physics"], "author": "Simulation Agent Framework Team", "license": "MIT", "devDependencies": {"@types/node": "^22.9.0", "@types/uuid": "^10.0.0", "typescript": "^5.6.3", "eslint": "^9.0.0", "@typescript-eslint/parser": "^8.18.0", "@typescript-eslint/eslint-plugin": "^8.18.0"}, "dependencies": {"@copilotkit/sdk-js": "1.3.7", "@langchain/core": "^0.3.18", "@langchain/google-genai": "^0.1.4", "@langchain/langgraph": "^0.2.57", "@langchain/langgraph-checkpoint": "^0.0.16", "@langchain/openai": "^0.3.14", "uuid": "^10.0.0", "zod": "^3.24.4"}, "overrides": {"@copilotkit/sdk-js": "1.3.7"}, "pnpm": {"overrides": {"@copilotkit/sdk-js": "1.3.7"}}}