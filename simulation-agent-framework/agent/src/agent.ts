/**
 * Simulation Agent Framework - Main Agent Implementation
 * Implements the hierarchical orchestrator-worker architecture from agent-design.md
 * Uses Google GenAI as the main LLM provider with CopilotKit integration
 */

import { StateGraph, MemorySaver } from "@langchain/langgraph";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { RunnableConfig } from "@langchain/core/runnables";
import { convertActionsToDynamicStructuredTools } from "@copilotkit/sdk-js/langchain";

import { SimulationState } from "./types.js";

// Import agent tools
import { 
  planSimulationWorkflow, 
  coordinateAgentWorkflow, 
  monitorSimulationStatus,
  handleSimulationError 
} from "./agents/orchestrator-agent.js";

import {
  executePrepWorkflow,
  interpretMultimodalInput,
  executeGenerativeCorrective,
  formulatePhysicsDomain,
  assembleSimulationPackage
} from "./agents/prep-agent.js";

// Import simulation tools
import {
  generateCADFromText,
  cleanGeometry,
  generateMesh,
  verifyMeshQuality
} from "./tools/geometry-tools.js";

import {
  parsePhysicsEquations,
  extractBoundaryConditions,
  lookupMaterialProperties,
  generateHydraConfig
} from "./tools/physics-tools.js";

import {
  executePhysicsNeMo,
  monitorSimulation,
  processSimulationResults,
  generateVisualization
} from "./tools/simulation-tools.js";

// Initialize Google GenAI model
const model = new ChatGoogleGenerativeAI({
  model: process.env.GOOGLE_MODEL || "gemini-1.5-pro",
  temperature: 0.3,
  apiKey: process.env.GOOGLE_API_KEY || "your_google_api_key_here",
});

// Main Chat Node - Entry point and coordinator
async function main_chat_node(state: SimulationState, config: RunnableConfig) {
  // Combine all available tools
  const localTools = [
    // Orchestrator tools
    planSimulationWorkflow,
    coordinateAgentWorkflow,
    monitorSimulationStatus,
    handleSimulationError,
    
    // PrepAgent tools
    executePrepWorkflow,
    interpretMultimodalInput,
    executeGenerativeCorrective,
    formulatePhysicsDomain,
    assembleSimulationPackage,
    
    // Geometry tools
    generateCADFromText,
    cleanGeometry,
    generateMesh,
    verifyMeshQuality,
    
    // Physics tools
    parsePhysicsEquations,
    extractBoundaryConditions,
    lookupMaterialProperties,
    generateHydraConfig,
    
    // Simulation tools
    executePhysicsNeMo,
    monitorSimulation,
    processSimulationResults,
    generateVisualization
  ];

  const modelWithTools = model.bindTools([
    ...convertActionsToDynamicStructuredTools(state.copilotkit?.actions || []),
    ...localTools,
  ]);

  const lastMessage = state.messages[state.messages.length - 1];
  
  // System message for simulation agent
  const systemMessage = {
    role: "system" as const,
    content: `You are the Simulation Agent Framework, an AI-powered engineering simulation platform following the NVIDIA technology stack architecture.

CORE CAPABILITIES:
🔧 Geometry Processing: Generate, clean, and mesh 3D geometries
⚡ Physics Simulation: Configure and execute PhysicsNeMo simulations  
📊 Results Analysis: Process and visualize simulation results
🤖 Multi-Agent Coordination: Orchestrate specialized worker agents

AGENT ARCHITECTURE:
• OrchestratorAgent: Main coordinator managing workflow
• PrepAgent: Geometry processing and domain preparation
• SolverAgent: PhysicsNeMo execution and monitoring
• PostAgent: Results processing and visualization

WORKFLOW PHASES:
1. Domain Preparation (PrepAgent)
2. Physics Simulation (SolverAgent) 
3. Post-Processing (PostAgent)

TECHNOLOGY STACK:
• 3D World: NVIDIA Omniverse (USD format)
• Physics Engine: NVIDIA PhysicsNeMo
• AI Framework: Google GenAI (Gemini)
• Orchestration: LangGraph multi-agent system

Always start by understanding the simulation requirements, then plan the workflow using the appropriate agents and tools.`,
    timestamp: new Date()
  };

  const response = await modelWithTools.invoke([
    systemMessage,
    ...state.messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }))
  ]);

  return {
    messages: [
      ...state.messages,
      {
        role: "assistant" as const,
        content: response.content as string,
        timestamp: new Date(),
        agent: "main_chat"
      }
    ]
  };
}

// PrepAgent Node - Specialized preprocessing agent
async function prep_agent_node(state: SimulationState, config: RunnableConfig) {
  const prepTools = [
    executePrepWorkflow,
    interpretMultimodalInput,
    executeGenerativeCorrective,
    formulatePhysicsDomain,
    assembleSimulationPackage,
    generateCADFromText,
    cleanGeometry,
    generateMesh,
    verifyMeshQuality,
    parsePhysicsEquations,
    extractBoundaryConditions,
    lookupMaterialProperties,
    generateHydraConfig
  ];

  const modelWithTools = model.bindTools(prepTools);

  const systemMessage = {
    role: "system" as const,
    content: `You are the PrepAgent - the specialized preprocessing and domain preparation agent.

CORE RESPONSIBILITIES:
🎨 Geometry Processing: Generate, analyze, clean, and optimize 3D geometries
🕸️ Mesh Generation: Create high-quality computational meshes
⚡ Physics Setup: Configure equations, boundary conditions, and materials
📦 Package Assembly: Create simulation-ready packages for SolverAgent

WORKFLOW CAPABILITIES:
• Text-to-CAD generation using parametric modeling
• AI-powered geometry cleanup and defeaturing
• Iterative mesh generation with quality verification
• Physics-informed domain formulation
• PhysicsNeMo Hydra configuration generation

QUALITY STANDARDS:
• Mesh quality > 80% (skewness, aspect ratio)
• Watertight geometry validation
• Physics consistency verification
• Complete boundary condition coverage

Always use the generative-corrective loop for optimization and ensure all outputs meet quality standards before handoff.`,
    timestamp: new Date()
  };

  const response = await modelWithTools.invoke([
    systemMessage,
    ...state.messages.slice(-3).map(msg => ({
      role: msg.role,
      content: msg.content
    }))
  ]);

  return {
    ...state,
    messages: [
      ...state.messages,
      {
        role: "assistant" as const,
        content: response.content as string,
        timestamp: new Date(),
        agent: "prep_agent"
      }
    ],
    activeAgent: "prep" as const
  };
}

// SolverAgent Node - Physics simulation execution
async function solver_agent_node(state: SimulationState, config: RunnableConfig) {
  const solverTools = [
    executePhysicsNeMo,
    monitorSimulation,
    processSimulationResults
  ];

  const modelWithTools = model.bindTools(solverTools);

  const systemMessage = {
    role: "system" as const,
    content: `You are the SolverAgent - specialized in physics simulation execution and monitoring.

CORE RESPONSIBILITIES:
🚀 PhysicsNeMo Execution: Launch and manage physics-AI simulations
📊 Convergence Monitoring: Track training progress and convergence
🔧 Parameter Optimization: Adjust solver settings for optimal performance
📈 Results Processing: Extract and validate simulation outputs

SIMULATION CAPABILITIES:
• Multi-GPU PhysicsNeMo training coordination
• Real-time convergence monitoring
• Adaptive parameter adjustment
• Error detection and recovery
• Results validation and extraction

QUALITY STANDARDS:
• Convergence criteria achievement
• Residual reduction verification
• Solution stability validation
• Performance metrics extraction

Monitor simulations closely and ensure convergence before proceeding to post-processing.`,
    timestamp: new Date()
  };

  const response = await modelWithTools.invoke([
    systemMessage,
    ...state.messages.slice(-3).map(msg => ({
      role: msg.role,
      content: msg.content
    }))
  ]);

  return {
    ...state,
    messages: [
      ...state.messages,
      {
        role: "assistant" as const,
        content: response.content as string,
        timestamp: new Date(),
        agent: "solver_agent"
      }
    ],
    activeAgent: "solver" as const
  };
}

// PostAgent Node - Results analysis and visualization
async function post_agent_node(state: SimulationState, config: RunnableConfig) {
  const postTools = [
    processSimulationResults,
    generateVisualization
  ];

  const modelWithTools = model.bindTools(postTools);

  const systemMessage = {
    role: "system" as const,
    content: `You are the PostAgent - specialized in results analysis and visualization.

CORE RESPONSIBILITIES:
📊 Results Analysis: Process and analyze simulation outputs
🎨 Visualization: Create professional visualizations using Omniverse
📋 Report Generation: Synthesize findings into comprehensive reports
🔍 Quality Assessment: Validate results and identify insights

VISUALIZATION CAPABILITIES:
• Omniverse USD scene generation
• Interactive 3D visualizations
• Contour plots and streamlines
• Animation and time-series analysis
• Professional report assembly

OUTPUT STANDARDS:
• High-resolution visualizations
• Interactive 3D scenes
• Comprehensive analysis reports
• Performance metrics summary
• Validation against requirements

Create professional-grade outputs suitable for engineering decision-making and presentation.`,
    timestamp: new Date()
  };

  const response = await modelWithTools.invoke([
    systemMessage,
    ...state.messages.slice(-3).map(msg => ({
      role: msg.role,
      content: msg.content
    }))
  ]);

  return {
    ...state,
    messages: [
      ...state.messages,
      {
        role: "assistant" as const,
        content: response.content as string,
        timestamp: new Date(),
        agent: "post_agent"
      }
    ],
    activeAgent: "post" as const
  };
}

// Routing function to determine next agent
function shouldContinue(state: SimulationState) {
  const lastMessage = state.messages[state.messages.length - 1];
  
  // Check if we need to route to a specific agent
  if (lastMessage.content.includes("PrepAgent") || lastMessage.content.includes("domain preparation")) {
    return "prep_agent";
  }
  if (lastMessage.content.includes("SolverAgent") || lastMessage.content.includes("physics simulation")) {
    return "solver_agent";
  }
  if (lastMessage.content.includes("PostAgent") || lastMessage.content.includes("visualization")) {
    return "post_agent";
  }
  
  return "__end__";
}

// Build the workflow graph
const workflow = new StateGraph<SimulationState>({
  channels: {
    messages: {
      reducer: (x, y) => x.concat(y),
      default: () => []
    },
    currentSimulation: {
      reducer: (x, y) => y ?? x,
      default: () => undefined
    },
    activeAgent: {
      reducer: (x, y) => y ?? x,
      default: () => "orchestrator"
    },
    workflowStep: {
      reducer: (x, y) => y ?? x,
      default: () => undefined
    },
    copilotkit: {
      reducer: (x, y) => y ?? x,
      default: () => undefined
    }
  }
})
  .addNode("main_chat_node", main_chat_node)
  .addNode("prep_agent", prep_agent_node)
  .addNode("solver_agent", solver_agent_node)
  .addNode("post_agent", post_agent_node)
  .addEdge("__start__", "main_chat_node")
  .addEdge("prep_agent", "main_chat_node")
  .addEdge("solver_agent", "main_chat_node")
  .addEdge("post_agent", "main_chat_node")
  .addConditionalEdges("main_chat_node", shouldContinue as any);

// Initialize memory for conversation persistence
const memory = new MemorySaver();

// Compile and export the workflow graph
export const graph = workflow.compile({
  checkpointer: memory,
});
