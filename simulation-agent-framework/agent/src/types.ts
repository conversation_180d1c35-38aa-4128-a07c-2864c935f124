/**
 * Simulation Agent Framework Types
 * Based on agent-design.md specifications
 */

import { z } from "zod";

// Core simulation state interface
export interface SimulationState {
  messages: Array<{
    role: "user" | "assistant" | "system";
    content: string;
    timestamp: Date;
    agent?: string;
  }>;
  
  // Current simulation context
  currentSimulation?: {
    id: string;
    status: "preparing" | "solving" | "post_processing" | "completed" | "error";
    description: string;
    geometry_uri?: string;
    config_uri?: string;
    results_uri?: string;
  };
  
  // Agent coordination
  activeAgent?: "orchestrator" | "prep" | "solver" | "post";
  workflowStep?: string;
  
  // CopilotKit integration
  copilotkit?: {
    actions?: Array<any>;
  };
}

// Input schema for simulation requests
export const SimulationRequestSchema = z.object({
  problem_description: z.string().describe("Natural language description of the simulation objective, physics, and key areas of interest"),
  initial_geometry_uri: z.string().optional().describe("URI pointing to initial 2D or 3D geometry file (STEP, IGES, USD)"),
  constraints_and_parameters: z.record(z.any()).optional().describe("Key-value map of specific parameters that override or supplement the problem description")
});

export type SimulationRequest = z.infer<typeof SimulationRequestSchema>;

// Geometry processing types
export interface GeometryInfo {
  uri: string;
  format: "STEP" | "IGES" | "STL" | "USD" | "OBJ";
  features: string[];
  boundingBox: {
    min: [number, number, number];
    max: [number, number, number];
  };
  volume?: number;
  surfaceArea?: number;
}

// Mesh quality metrics
export interface MeshQuality {
  elementCount: number;
  nodeCount: number;
  skewness: {
    min: number;
    max: number;
    average: number;
  };
  aspectRatio: {
    min: number;
    max: number;
    average: number;
  };
  quality: "excellent" | "good" | "acceptable" | "poor";
}

// Physics configuration
export interface PhysicsConfig {
  equations: string[];
  boundaryConditions: Array<{
    type: string;
    location: string;
    values: Record<string, number>;
  }>;
  materialProperties: Record<string, any>;
  solverSettings: {
    maxSteps: number;
    convergenceCriteria: number;
    outputVariables: string[];
  };
}

// Simulation results
export interface SimulationResults {
  convergence: {
    achieved: boolean;
    finalResidual: number;
    iterations: number;
  };
  outputFiles: {
    vtk?: string;
    checkpoints?: string;
    logs?: string;
  };
  metrics: Record<string, number>;
  visualizations?: string[];
}

// Agent communication protocols
export interface AgentMessage {
  from: string;
  to: string;
  type: "task_assignment" | "status_update" | "result" | "error";
  payload: any;
  timestamp: Date;
}

// Tool definitions for agents
export interface SimulationTool {
  name: string;
  description: string;
  parameters: z.ZodSchema;
  execute: (args: any) => Promise<any>;
}
