# Simulation Agent Framework - Environment Configuration

# Google AI (Gemini) Configuration - Primary LLM Provider
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_MODEL=gemini-1.5-pro

# OpenAI Configuration - Backup/Alternative LLM Provider
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini
OPENAI_BASE_URL=https://api.openai.com/v1

# NVIDIA Technology Stack Configuration
# Note: These are mock configurations for development
# In production, replace with actual NVIDIA service endpoints

# NVIDIA Omniverse Configuration
OMNIVERSE_NUCLEUS_URL=omniverse://localhost/simulation
OMNIVERSE_API_KEY=your_omniverse_api_key_here

# NVIDIA PhysicsNeMo Configuration
PHYSICSNEMO_API_URL=http://localhost:8080/physicsnemo
PHYSICSNEMO_API_KEY=your_physicsnemo_api_key_here

# NVIDIA NeMo Agent Toolkit Configuration
NEMO_AGENT_TOOLKIT_URL=http://localhost:8090/nemo-agents
NEMO_AGENT_TOOLKIT_API_KEY=your_nemo_toolkit_api_key_here

# Simulation Storage Configuration
SIMULATION_STORAGE_PATH=/tmp/simulations
SIMULATION_RESULTS_PATH=/tmp/results
SIMULATION_CACHE_PATH=/tmp/cache

# Database Configuration (for simulation metadata)
DATABASE_URL=sqlite:///tmp/simulation_framework.db

# Logging and Monitoring
LOG_LEVEL=info
ENABLE_TELEMETRY=true
METRICS_ENDPOINT=http://localhost:9090/metrics

# Development Settings
NODE_ENV=development
DEBUG=simulation-agent:*
