# 🦙 Ollama Local AI Setup for BiteBase Intelligence

## Quick Setup (2 minutes):

### 1. Install Ollama:
```bash
# macOS/Linux
curl -fsSL https://ollama.ai/install.sh | sh

# Windows (PowerShell)
iwr https://ollama.ai/install.ps1 -useb | iex
```

### 2. Start Ollama on port 4141:
```bash
# Set custom port and start Ollama
OLLAMA_HOST=0.0.0.0:4141 ollama serve
```

### 3. Pull the Llama model:
```bash
# In a new terminal window
OLLAMA_HOST=http://localhost:4141 ollama pull llama3.1:8b
```

### 4. Verify setup:
```bash
curl http://localhost:4141/api/tags
```
Should return JSON with available models.

## System Configuration:

✅ **Frontend API Route**: Already configured for Ollama  
✅ **LangGraph Agents**: Already configured for `llama3.1:8b`  
✅ **Environment Variables**: Set to use port 4141  
✅ **No API Keys Required**: Everything runs locally  

## Launch Commands:
```bash
# Terminal 1: Start Ollama
OLLAMA_HOST=0.0.0.0:4141 ollama serve

# Terminal 2: Start LangGraph Server  
cd agent && npm run start

# Terminal 3: Start Frontend
npm run dev
```

## Model Options:
- **llama3.1:8b** (Default, ~4.7GB) - Good balance of speed/quality
- **llama3.1:70b** (~40GB) - Higher quality, needs more RAM
- **codellama:7b** (~3.8GB) - Optimized for code tasks
- **mistral:7b** (~4.1GB) - Fast alternative

Switch models by updating `.env.local`:
```env
OLLAMA_MODEL=mistral:7b
```

## Benefits:
🔒 **Privacy**: All AI processing happens locally  
💰 **Cost**: No API usage fees  
⚡ **Speed**: No network latency  
🎯 **Control**: Full control over model and responses  

## Troubleshooting:
- **Port in use?** Check with `lsof -i :4141` and kill process
- **Model not found?** Run `OLLAMA_HOST=http://localhost:4141 ollama pull llama3.1:8b`
- **Connection refused?** Ensure Ollama is running with `OLLAMA_HOST=0.0.0.0:4141 ollama serve`

**Result**: Complete local AI-powered restaurant market research platform! 🎉
