/**
 * BiteBase Intelligence Agent - Agentic AI Framework
 *
 * This is the main entry point for the BiteBase Intelligence agentic AI system.
 * It implements a comprehensive multi-agent framework for restaurant market research,
 * site selection, competitive intelligence, and business optimization.
 *
 * Based on the Culinary Compass AI design document specifications.
 */

import { z } from "zod";
import { RunnableConfig } from "@langchain/core/runnables";
import { tool } from "@langchain/core/tools";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { AIMessage, SystemMessage, HumanMessage, BaseMessage } from "@langchain/core/messages";
import { MemorySaver, START, StateGraph, END } from "@langchain/langgraph";
import { Annotation } from "@langchain/langgraph";
import { ChatOpenAI } from "@langchain/openai";

// CopilotKit integration
import { convertActionsToDynamicStructuredTools } from "@copilotkit/sdk-js/langgraph";
import { CopilotKitStateAnnotation } from "@copilotkit/sdk-js/langgraph";

// --- Specialized Agent Imports ---
import { menuPerformanceAgentConfig, menuPerformanceTools } from "./agents/menu-performance-agent";
import { costProfitabilityAgentConfig, costProfitabilityTools } from "./agents/cost-profitability-agent";
import { trendAnalysisAgentConfig, trendAnalysisTools } from "./agents/trend-analysis-agent";
import { dynamicPricingAgentConfig, dynamicPricingTools } from "./agents/dynamic-pricing-agent";
import { customerDensityAgentConfig, customerDensityTools } from "./agents/customer-density-agent";
import { competitorAnalysisAgentConfig, competitorAnalysisTools } from "./agents/competitor-analysis-agent";
import { salesForecastingAgentConfig, salesForecastingTools } from "./agents/sales-forecasting-agent";
import { customerSegmentationAgentConfig, customerSegmentationTools } from "./agents/customer-segmentation-agent";
import { campaignManagementAgentConfig, campaignManagementTools } from "./agents/campaign-management-agent";
import { loyaltyTrackingAgentConfig, loyaltyTrackingTools } from "./agents/loyalty-tracking-agent";
import { inventoryTrackingAgentConfig, inventoryTrackingTools } from "./agents/inventory-tracking-agent";
import { sentimentAnalysisAgentConfig, sentimentAnalysisTools } from "./agents/sentiment-analysis-agent";

// --- Agent Registry ---
export const agentRegistry = [
  menuPerformanceAgentConfig,
  costProfitabilityAgentConfig,
  trendAnalysisAgentConfig,
  dynamicPricingAgentConfig,
  customerDensityAgentConfig,
  competitorAnalysisAgentConfig,
  salesForecastingAgentConfig,
  customerSegmentationAgentConfig,
  campaignManagementAgentConfig,
  loyaltyTrackingAgentConfig,
  inventoryTrackingAgentConfig,
  sentimentAnalysisAgentConfig
];

// --- Tool Registry (for orchestration, if needed) ---
export const allAgentTools = [
  ...menuPerformanceTools,
  ...costProfitabilityTools,
  ...trendAnalysisTools,
  ...dynamicPricingTools,
  ...customerDensityTools,
  ...competitorAnalysisTools,
  ...salesForecastingTools,
  ...customerSegmentationTools,
  ...campaignManagementTools,
  ...loyaltyTrackingTools,
  ...inventoryTrackingTools,
  ...sentimentAnalysisTools
];
// ===== AGENT STATE DEFINITIONS =====

// Restaurant concept and location data
export interface RestaurantConcept {
  name: string;
  cuisine: string;
  serviceModel: 'fast-casual' | 'fine-dining' | 'quick-service' | 'casual-dining';
  pricePoint: 'budget' | 'mid-range' | 'premium' | 'luxury';
  targetDemographic: string;
  uniqueSellingPoints: string[];
}

export interface LocationData {
  address: string;
  coordinates: { lat: number; lng: number };
  demographics: Record<string, any>;
  footTraffic: Record<string, number>;
  competitors: CompetitorData[];
  realEstate: RealEstateData;
  viabilityScore?: number;
}

export interface CompetitorData {
  name: string;
  cuisine: string;
  priceRange: string;
  rating: number;
  reviewCount: number;
  distance: number;
  strengths: string[];
  weaknesses: string[];
}

export interface RealEstateData {
  rentPerSqFt: number;
  squareFootage: number;
  zoning: string;
  parkingSpaces: number;
  visibility: 'high' | 'medium' | 'low';
}

export interface AnalysisResult {
  type: 'site-analysis' | 'competitive-intelligence' | 'financial-forecast' | 'market-research';
  confidence: number;
  summary: string;
  recommendations: string[];
  data: Record<string, any>;
  timestamp: Date;
}

// Agent workflow state
export interface WorkflowState {
  currentStep: string;
  progress: number;
  status: 'idle' | 'analyzing' | 'waiting-approval' | 'completed' | 'error';
  pendingApprovals: ApprovalRequest[];
}

export interface ApprovalRequest {
  id: string;
  action: string;
  description: string;
  risks: string[];
  benefits: string[];
  confidence: number;
}

// Main agent state annotation
export const AgentStateAnnotation = Annotation.Root({
  // Core restaurant business data
  concept: Annotation<RestaurantConcept | null>,
  targetLocation: Annotation<string | null>,
  locationCandidates: Annotation<LocationData[]>,
  selectedLocation: Annotation<LocationData | null>,

  // Analysis results and insights
  analysisResults: Annotation<AnalysisResult[]>,
  marketInsights: Annotation<Record<string, any>>,
  financialProjections: Annotation<Record<string, any>>,

  // Workflow management
  workflow: Annotation<WorkflowState>,
  userPreferences: Annotation<Record<string, any>>,

  // Agent coordination
  activeAgents: Annotation<string[]>,
  agentResults: Annotation<Record<string, any>>,

  // CopilotKit integration
  ...CopilotKitStateAnnotation.spec,
});

export type AgentState = typeof AgentStateAnnotation.State;

// ===== SPECIALIZED TOOLS FOR RESTAURANT INTELLIGENCE =====

// Site Analysis Tools
const analyzeSiteViability = tool(
  async (args) => {
    // Simulate comprehensive site analysis
    const viabilityScore = Math.floor(Math.random() * 40) + 60; // 60-100 range
    const analysis = {
      viabilityScore,
      footTrafficScore: Math.floor(Math.random() * 30) + 70,
      demographicAlignment: Math.floor(Math.random() * 25) + 75,
      competitiveAdvantage: Math.floor(Math.random() * 35) + 65,
      realEstateViability: Math.floor(Math.random() * 20) + 80,
      recommendations: [
        "High foot traffic during lunch hours (11 AM - 2 PM)",
        "Strong demographic alignment with target customer profile",
        "Limited direct competition within 0.5 mile radius",
        "Reasonable rent-to-revenue ratio projected at 8-12%"
      ]
    };

    return `Site Viability Analysis for ${args.address}:

Overall Viability Score: ${viabilityScore}/100

Key Metrics:
• Foot Traffic Score: ${analysis.footTrafficScore}/100
• Demographic Alignment: ${analysis.demographicAlignment}/100
• Competitive Advantage: ${analysis.competitiveAdvantage}/100
• Real Estate Viability: ${analysis.realEstateViability}/100

Recommendations:
${analysis.recommendations.map(r => `• ${r}`).join('\n')}

This location shows ${viabilityScore >= 80 ? 'excellent' : viabilityScore >= 70 ? 'good' : 'moderate'} potential for a ${args.concept} restaurant concept.`;
  },
  {
    name: "analyzeSiteViability",
    description: "Analyze the viability of a specific location for a restaurant concept",
    schema: z.object({
      address: z.string().describe("The address to analyze"),
      concept: z.string().describe("The restaurant concept (e.g., 'fast-casual pizza', 'fine dining Italian')"),
    }),
  }
);

const identifyCompetitors = tool(
  async (args) => {
    // Simulate competitor discovery and analysis
    const competitors = [
      {
        name: "Tony's Pizzeria",
        cuisine: "Italian",
        distance: "0.3 miles",
        rating: 4.2,
        priceRange: "$$",
        strengths: ["Authentic recipes", "Fast service"],
        weaknesses: ["Limited seating", "Outdated decor"]
      },
      {
        name: "Burger Junction",
        cuisine: "American",
        distance: "0.5 miles",
        rating: 3.8,
        priceRange: "$",
        strengths: ["Low prices", "Quick delivery"],
        weaknesses: ["Poor food quality", "Slow service"]
      },
      {
        name: "Sakura Sushi",
        cuisine: "Japanese",
        distance: "0.7 miles",
        rating: 4.5,
        priceRange: "$$$",
        strengths: ["Fresh ingredients", "Excellent service"],
        weaknesses: ["High prices", "Limited parking"]
      }
    ];

    return `Competitive Analysis for ${args.location}:

Found ${competitors.length} key competitors within 1-mile radius:

${competitors.map(c => `
🏪 ${c.name} (${c.distance})
   Cuisine: ${c.cuisine} | Rating: ${c.rating}⭐ | Price: ${c.priceRange}
   Strengths: ${c.strengths.join(', ')}
   Weaknesses: ${c.weaknesses.join(', ')}
`).join('')}

Market Opportunity:
• Gap identified in ${args.concept} segment
• Average competitor rating: ${(competitors.reduce((sum, c) => sum + c.rating, 0) / competitors.length).toFixed(1)}
• Price point opportunities in mid-range segment
• Service quality improvements could provide competitive advantage`;
  },
  {
    name: "identifyCompetitors",
    description: "Identify and analyze competitors in a specific location",
    schema: z.object({
      location: z.string().describe("The location to analyze competitors for"),
      concept: z.string().describe("The restaurant concept to compare against"),
    }),
  }
);

const generateFinancialForecast = tool(
  async (args) => {
    // Simulate financial modeling
    const projectedRevenue = Math.floor(Math.random() * 500000) + 750000; // $750K-$1.25M
    const foodCostPercent = Math.floor(Math.random() * 5) + 28; // 28-32%
    const laborCostPercent = Math.floor(Math.random() * 5) + 30; // 30-34%
    const rentCostPercent = Math.floor(Math.random() * 3) + 8; // 8-10%

    const foodCost = projectedRevenue * (foodCostPercent / 100);
    const laborCost = projectedRevenue * (laborCostPercent / 100);
    const rentCost = projectedRevenue * (rentCostPercent / 100);
    const otherExpenses = projectedRevenue * 0.15; // 15% other expenses

    const totalExpenses = foodCost + laborCost + rentCost + otherExpenses;
    const netProfit = projectedRevenue - totalExpenses;
    const profitMargin = (netProfit / projectedRevenue) * 100;

    return `Financial Forecast for ${args.concept} at ${args.location}:

📊 YEAR 1 PROJECTIONS:

Revenue: $${projectedRevenue.toLocaleString()}

Operating Expenses:
• Food Costs: $${foodCost.toLocaleString()} (${foodCostPercent}%)
• Labor Costs: $${laborCost.toLocaleString()} (${laborCostPercent}%)
• Rent: $${rentCost.toLocaleString()} (${rentCostPercent}%)
• Other Expenses: $${otherExpenses.toLocaleString()} (15%)

Net Profit: $${netProfit.toLocaleString()}
Profit Margin: ${profitMargin.toFixed(1)}%

💡 Key Insights:
• Break-even point: Month ${Math.floor(Math.random() * 6) + 8}
• ROI Timeline: ${Math.floor(Math.random() * 12) + 24} months
• Cash flow positive by Month ${Math.floor(Math.random() * 4) + 6}
• ${profitMargin >= 15 ? 'Excellent' : profitMargin >= 10 ? 'Good' : 'Moderate'} profitability projected`;
  },
  {
    name: "generateFinancialForecast",
    description: "Generate detailed financial projections for a restaurant concept",
    schema: z.object({
      concept: z.string().describe("The restaurant concept"),
      location: z.string().describe("The target location"),
      investmentAmount: z.number().optional().nullable().describe("Initial investment amount"),
    }),
  }
);

const analyzeMarketTrends = tool(
  async (args) => {
    const trends = [
      "Plant-based menu options showing 35% growth year-over-year",
      "Ghost kitchens and delivery-only concepts gaining traction",
      "Sustainable sourcing becoming key differentiator",
      "Technology integration (QR menus, mobile ordering) now expected",
      "Local and artisanal ingredients commanding premium pricing",
      "Health-conscious options driving menu innovation"
    ];

    const relevantTrends = trends.slice(0, Math.floor(Math.random() * 3) + 3);

    return `Market Trend Analysis for ${args.cuisine} Restaurants:

🔍 CURRENT MARKET TRENDS:

${relevantTrends.map((trend, i) => `${i + 1}. ${trend}`).join('\n')}

📈 OPPORTUNITY ASSESSMENT:
• Market growth rate: ${Math.floor(Math.random() * 8) + 5}% annually
• Consumer spending on dining: ${Math.floor(Math.random() * 15) + 85}% of pre-pandemic levels
• Delivery market penetration: ${Math.floor(Math.random() * 20) + 40}%

🎯 STRATEGIC RECOMMENDATIONS:
• Incorporate trending elements into concept design
• Focus on digital-first customer experience
• Emphasize sustainability and health-conscious options
• Consider hybrid dine-in/delivery model`;
  },
  {
    name: "analyzeMarketTrends",
    description: "Analyze current market trends for a specific cuisine or restaurant type",
    schema: z.object({
      cuisine: z.string().describe("The cuisine type to analyze trends for"),
      location: z.string().optional().nullable().describe("Geographic market to focus on"),
    }),
  }
);

// Workflow coordination tool
const coordinateAgentWorkflow = tool(
  async (args) => {
    const workflows = {
      'site-selection': [
        'Define restaurant concept',
        'Identify target markets',
        'Analyze potential locations',
        'Conduct competitive analysis',
        'Generate financial projections',
        'Recommend optimal site'
      ],
      'market-research': [
        'Analyze market trends',
        'Identify target demographics',
        'Study competitor landscape',
        'Assess market opportunities',
        'Generate market entry strategy'
      ],
      'business-planning': [
        'Validate concept-market fit',
        'Develop financial model',
        'Create operational plan',
        'Design marketing strategy',
        'Prepare investor presentation'
      ]
    };

    const selectedWorkflow = workflows[args.workflowType as keyof typeof workflows] || workflows['site-selection'];

    return `Initiating ${args.workflowType} workflow:

📋 WORKFLOW STEPS:
${selectedWorkflow.map((step, i) => `${i + 1}. ${step}`).join('\n')}

🤖 AGENT COORDINATION:
• Planning Agent: Orchestrating overall workflow
• Research Agent: Gathering market intelligence
• Location Agent: Analyzing site viability
• Financial Agent: Generating projections
• Competitive Agent: Monitoring competition

⏱️ ESTIMATED TIMELINE: ${Math.floor(Math.random() * 10) + 15} minutes

Ready to begin comprehensive analysis. Each step will provide detailed insights and recommendations.`;
  },
  {
    name: "coordinateAgentWorkflow",
    description: "Coordinate a multi-agent workflow for restaurant intelligence",
    schema: z.object({
      workflowType: z.enum(['site-selection', 'market-research', 'business-planning']).describe("Type of workflow to execute"),
      priority: z.enum(['low', 'medium', 'high']).optional().nullable().describe("Workflow priority level"),
    }),
  }
);

// Compile all tools
const tools = [
  analyzeSiteViability,
  identifyCompetitors,
  generateFinancialForecast,
  analyzeMarketTrends,
  coordinateAgentWorkflow
];

// ===== SPECIALIZED AGENT NODES =====

// Planning Agent - Orchestrates workflows and coordinates other agents
async function planning_agent(state: AgentState, config: RunnableConfig) {
  const model = new ChatOpenAI({ 
    temperature: 0.3, 
    model: process.env.OPENAI_MODEL || "gpt-4o-mini",
    configuration: { 
      baseURL: process.env.OPENAI_BASE_URL || "https://api.openai.com/v1", 
      apiKey: process.env.OPENAI_API_KEY || "your_api_key_here" 
    } 
  });

  const modelWithTools = model.bindTools([
    ...convertActionsToDynamicStructuredTools(state.copilotkit?.actions || []),
    coordinateAgentWorkflow,
  ]);

  const systemMessage = new SystemMessage({
    content: `You are the Planning Agent for BiteBase Intelligence, a sophisticated restaurant market research platform.

Your role is to:
- Orchestrate complex analytical workflows
- Coordinate between specialized agents (Research, Location, Competitive, Financial)
- Break down user requests into actionable steps
- Ensure comprehensive analysis coverage
- Provide strategic guidance and recommendations

You have access to a multi-agent system that can perform:
- Site viability analysis
- Competitive intelligence
- Financial forecasting
- Market trend analysis
- Location optimization

Always start by understanding the user's goals, then coordinate the appropriate workflow to deliver comprehensive insights.`,
  });

  const response = await modelWithTools.invoke(
    [systemMessage, ...state.messages],
    config
  );

  return {
    messages: response,
    workflow: {
      ...state.workflow,
      currentStep: "planning",
      status: "analyzing" as const,
    },
    activeAgents: [...(state.activeAgents || []), "planning"],
  };
}

// Research Agent - Market research and trend analysis
async function research_agent(state: AgentState, config: RunnableConfig) {
  const model = new ChatOpenAI({ 
    temperature: 0.2, 
    model: process.env.OPENAI_MODEL || "gpt-4o-mini",
    configuration: { 
      baseURL: process.env.OPENAI_BASE_URL || "https://api.openai.com/v1", 
      apiKey: process.env.OPENAI_API_KEY || "your_api_key_here" 
    } 
  });

  const modelWithTools = model.bindTools([
    analyzeMarketTrends,
  ]);

  const systemMessage = new SystemMessage({
    content: `You are the Research Agent for BiteBase Intelligence.

Your expertise includes:
- Market trend analysis and forecasting
- Consumer behavior insights
- Industry benchmarking
- Demographic analysis
- Economic impact assessment

You provide data-driven insights about market opportunities, consumer preferences, and industry trends that inform strategic restaurant decisions.

Focus on delivering actionable market intelligence with specific recommendations.`,
  });

  const response = await modelWithTools.invoke(
    [systemMessage, ...state.messages],
    config
  );

  return {
    messages: response,
    activeAgents: [...(state.activeAgents || []), "research"],
  };
}

// Location Agent - Site analysis and location optimization
async function location_agent(state: AgentState, config: RunnableConfig) {
  const model = new ChatOpenAI({ 
    temperature: 0.1, 
    model: process.env.OPENAI_MODEL || "gpt-4o-mini",
    configuration: { 
      baseURL: process.env.OPENAI_BASE_URL || "https://api.openai.com/v1", 
      apiKey: process.env.OPENAI_API_KEY || "your_api_key_here" 
    } 
  });

  const modelWithTools = model.bindTools([
    analyzeSiteViability,
  ]);

  const systemMessage = new SystemMessage({
    content: `You are the Location Agent for BiteBase Intelligence.

Your specialization includes:
- Site viability scoring and analysis
- Foot traffic pattern analysis
- Demographic alignment assessment
- Real estate evaluation
- Location optimization recommendations

You analyze potential restaurant locations using predictive models that consider foot traffic, demographics, accessibility, visibility, and market dynamics.

Provide detailed location assessments with quantitative scores and specific recommendations.`,
  });

  const response = await modelWithTools.invoke(
    [systemMessage, ...state.messages],
    config
  );

  return {
    messages: response,
    activeAgents: [...(state.activeAgents || []), "location"],
  };
}

// Competitive Agent - Competitive intelligence and positioning
async function competitive_agent(state: AgentState, config: RunnableConfig) {
  const model = new ChatOpenAI({ 
    temperature: 0.2, 
    model: process.env.OPENAI_MODEL || "gpt-4o-mini",
    configuration: { 
      baseURL: process.env.OPENAI_BASE_URL || "https://api.openai.com/v1", 
      apiKey: process.env.OPENAI_API_KEY || "your_api_key_here" 
    } 
  });

  const modelWithTools = model.bindTools([
    identifyCompetitors,
  ]);

  const systemMessage = new SystemMessage({
    content: `You are the Competitive Intelligence Agent for BiteBase Intelligence.

Your expertise covers:
- Competitor identification and profiling
- Market positioning analysis
- Pricing strategy assessment
- Competitive advantage identification
- Market gap analysis

You analyze the competitive landscape to identify opportunities for differentiation and strategic positioning.

Provide comprehensive competitive analysis with specific strategic recommendations.`,
  });

  const response = await modelWithTools.invoke(
    [systemMessage, ...state.messages],
    config
  );

  return {
    messages: response,
    activeAgents: [...(state.activeAgents || []), "competitive"],
  };
}

// Financial Agent - Financial modeling and projections
async function financial_agent(state: AgentState, config: RunnableConfig) {
  const model = new ChatOpenAI({ 
    temperature: 0.1, 
    model: process.env.OPENAI_MODEL || "gpt-4o-mini",
    configuration: { 
      baseURL: process.env.OPENAI_BASE_URL || "https://api.openai.com/v1", 
      apiKey: process.env.OPENAI_API_KEY || "your_api_key_here" 
    } 
  });

  const modelWithTools = model.bindTools([
    generateFinancialForecast,
  ]);

  const systemMessage = new SystemMessage({
    content: `You are the Financial Agent for BiteBase Intelligence.

Your capabilities include:
- Revenue forecasting and financial modeling
- Cost structure analysis
- Profitability projections
- ROI calculations
- Break-even analysis
- Investment requirement assessment

You create comprehensive financial models that help entrepreneurs understand the economic viability of their restaurant concepts.

Provide detailed financial analysis with clear assumptions and actionable insights.`,
  });

  const response = await modelWithTools.invoke(
    [systemMessage, ...state.messages],
    config
  );

  return {
    messages: response,
    activeAgents: [...(state.activeAgents || []), "financial"],
  };
}

// Import orchestrator functions commented out - using local tools instead
// import {
//   workflowOrchestrator,
//   agentStatusTracker,
//   determineNextAgent,
//   executeAgent,
//   allAgentTools,
//   WORKFLOW_DEFINITIONS
// } from "./orchestrator";

// Main Chat Node - Entry point and coordinator
async function main_chat_node(state: AgentState, config: RunnableConfig) {
  const model = new ChatOpenAI({ 
    temperature: 0.3, 
    model: process.env.OPENAI_MODEL || "gpt-4o-mini",
    configuration: { 
      baseURL: process.env.OPENAI_BASE_URL || "https://api.openai.com/v1", 
      apiKey: process.env.OPENAI_API_KEY || "your_api_key_here" 
    } 
  });

  // Use local tools that are actually defined
  const localTools = [
    analyzeSiteViability,
    identifyCompetitors,
    generateFinancialForecast,
    analyzeMarketTrends,
    coordinateAgentWorkflow
  ];
  
  const modelWithTools = model.bindTools([
    ...convertActionsToDynamicStructuredTools(state.copilotkit?.actions || []),
    ...localTools,
  ]);

  // Get workflow status (simplified - no orchestrator)
  const workflowStatus = { active: true, phase: 'consultation' };
  const activeAgents = ['main_chat'];

  const systemMessage = new SystemMessage({
    content: `You are BiteBase Intelligence, an advanced agentic AI system for restaurant market research and business intelligence.

You help restaurant entrepreneurs and investors make data-driven decisions through:

🎯 CORE CAPABILITIES:
- Site Selection & Viability Analysis (22-min comprehensive workflow)
- Competitive Intelligence & Market Positioning (16-min analysis workflow)
- Financial Modeling & Revenue Forecasting (detailed projections)
- Market Research & Trend Analysis (industry insights)
- Business Planning & Strategy Development (26-min complete workflow)

🤖 SPECIALIZED AGENT NETWORK:
- Planning Agent: Orchestrates workflows and coordinates other agents
- Research Agent: Market trends, demographics, industry analysis
- Location Agent: Site viability, foot traffic, real estate analysis
- Competitive Agent: Competitor analysis, market positioning
- Financial Agent: Revenue forecasting, financial modeling

🔄 AVAILABLE WORKFLOWS:
1. Site Selection: Complete location analysis and recommendation
2. Market Research: Comprehensive market opportunity assessment
3. Business Planning: Full business plan development and validation

📊 CURRENT STATUS:
- Workflow: ${workflowStatus.phase || 'None active'}
- Progress: ${state.workflow?.progress || 0}%
- Active Agents: ${activeAgents.join(', ') || 'None'}
- Status: ${state.workflow?.status || 'Ready'}

🎯 INTERACTION APPROACH:
1. Understand user's specific goals and context
2. Recommend and initiate appropriate workflows
3. Coordinate specialized agents for comprehensive analysis
4. Synthesize insights from multiple agents
5. Provide actionable recommendations and next steps

Always start by understanding what the user wants to accomplish, then leverage the appropriate agents and workflows to deliver comprehensive, data-driven insights.`,
  });

  const response = await modelWithTools.invoke(
    [systemMessage, ...state.messages],
    config
  );

  // Update agent status (simplified - no status tracker)
  // agentStatusTracker removed for standalone operation

  return {
    messages: response,
    workflow: {
      ...state.workflow,
      currentStep: "main_chat",
      status: "analyzing" as const,
    },
  };
}

// ===== WORKFLOW ROUTING AND COORDINATION =====

// Intelligent routing function for multi-agent coordination
function routeToAgent(state: AgentState): string {
  const lastMessage = state.messages[state.messages.length - 1] as AIMessage;

  // Check for tool calls first
  if (lastMessage.tool_calls?.length) {
    const actions = state.copilotkit?.actions;
    const toolCallName = lastMessage.tool_calls[0].name;

    // Route to tool node if not a CopilotKit action
    if (!actions || actions.every((action) => action.name !== toolCallName)) {
      return "tool_node";
    }
  }

  // Analyze message content for agent routing
  const messageContent = lastMessage.content?.toString().toLowerCase() || '';

  // Route to specialized agents based on content analysis
  if (messageContent.includes('location') || messageContent.includes('site') || messageContent.includes('address')) {
    return "location_agent";
  }

  if (messageContent.includes('competitor') || messageContent.includes('competition') || messageContent.includes('market position')) {
    return "competitive_agent";
  }

  if (messageContent.includes('financial') || messageContent.includes('revenue') || messageContent.includes('profit') || messageContent.includes('forecast')) {
    return "financial_agent";
  }

  if (messageContent.includes('trend') || messageContent.includes('market research') || messageContent.includes('industry')) {
    return "research_agent";
  }

  if (messageContent.includes('workflow') || messageContent.includes('plan') || messageContent.includes('strategy')) {
    return "planning_agent";
  }

  // Default to end if no specific routing needed
  return END;
}

// Workflow continuation logic
function shouldContinue(state: AgentState): string {
  const lastMessage = state.messages[state.messages.length - 1] as AIMessage;

  // Handle tool calls
  if (lastMessage.tool_calls?.length) {
    const actions = state.copilotkit?.actions;
    const toolCallName = lastMessage.tool_calls[0].name;

    if (!actions || actions.every((action) => action.name !== toolCallName)) {
      return "tool_node";
    }
  }

  // Check workflow status
  const workflowStatus = state.workflow?.status;
  if (workflowStatus === 'waiting-approval') {
    return END; // Wait for human approval
  }

  // Continue workflow if in progress
  if (workflowStatus === 'analyzing' && state.activeAgents?.length) {
    return "main_chat_node"; // Return to main coordinator
  }

  return END;
}

// Agent completion routing
function routeAfterAgent(state: AgentState): string {
  const workflow = state.workflow;

  // If workflow is still in progress, return to main chat
  if (workflow?.status === 'analyzing') {
    return "main_chat_node";
  }

  // If workflow requires approval, end and wait
  if (workflow?.status === 'waiting-approval') {
    return END;
  }

  return END;
}

// ===== WORKFLOW GRAPH DEFINITION =====

const workflow = new StateGraph(AgentStateAnnotation)
  // Add all agent nodes
  .addNode("main_chat_node", main_chat_node)
  .addNode("planning_agent", planning_agent)
  .addNode("research_agent", research_agent)
  .addNode("location_agent", location_agent)
  .addNode("competitive_agent", competitive_agent)
  .addNode("financial_agent", financial_agent)
  .addNode("tool_node", new ToolNode(tools))

  // Define entry point
  .addEdge(START, "main_chat_node")

  // Tool node routing
  .addEdge("tool_node", "main_chat_node")

  // Agent completion routing - all agents return to main chat
  .addEdge("planning_agent", "main_chat_node")
  .addEdge("research_agent", "main_chat_node")
  .addEdge("location_agent", "main_chat_node")
  .addEdge("competitive_agent", "main_chat_node")
  .addEdge("financial_agent", "main_chat_node")

  // Conditional routing from main chat node
  .addConditionalEdges("main_chat_node", shouldContinue as any);

// Initialize memory for conversation persistence
const memory = new MemorySaver();

// Compile the workflow graph
export const graph = workflow.compile({
  checkpointer: memory,
});

// Export agent types for frontend integration
export const AGENT_TYPES = {
  MAIN_CHAT: "main_chat_node",
  PLANNING: "planning_agent",
  RESEARCH: "research_agent",
  LOCATION: "location_agent",
  COMPETITIVE: "competitive_agent",
  FINANCIAL: "financial_agent",
  TOOLS: "tool_node"
} as const;

// Export workflow types
export const WORKFLOW_TYPES = {
  SITE_SELECTION: "site-selection",
  MARKET_RESEARCH: "market-research",
  BUSINESS_PLANNING: "business-planning"
} as const;
