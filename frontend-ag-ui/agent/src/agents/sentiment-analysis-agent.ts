/**
 * Sentiment Analysis Agent - Add-On Features Module
 * 
 * Analyzes customer reviews, social media mentions, and feedback to gauge brand sentiment
 * and identify improvement opportunities.
 */

import { z } from "zod";
import { tool } from "@langchain/core/tools";

// Review Sentiment Analysis Tool
export const analyzeReviewSentiment = tool(
  async (args) => {
    const platform = args.platform || "all";
    const timeframe = args.timeframe || "30days";
    
    const mockSentimentData = {
      platform: platform,
      timeframe: timeframe,
      lastUpdated: "2024-09-10 15:45:22",
      overallSentiment: {
        score: 3.8,
        trend: "improving",
        changeFromPrevious: +0.3,
        totalReviews: 847,
        distribution: {
          positive: 65.2,
          neutral: 22.1, 
          negative: 12.7
        }
      },
      platformBreakdown: [
        {
          platform: "Google Reviews",
          totalReviews: 312,
          averageRating: 4.1,
          sentimentScore: 4.1,
          trend: "stable",
          recentReviews: [
            {
              date: "2024-09-10",
              rating: 5,
              sentiment: "positive",
              text: "Amazing food and service! The new pasta dish was incredible.",
              keyPhrases: ["amazing food", "incredible pasta", "great service"],
              emotionalTone: "enthusiastic"
            },
            {
              date: "2024-09-09", 
              rating: 2,
              sentiment: "negative",
              text: "Long wait time and food was cold when it arrived.",
              keyPhrases: ["long wait", "cold food", "poor service"],
              emotionalTone: "frustrated"
            },
            {
              date: "2024-09-09",
              rating: 4,
              sentiment: "positive", 
              text: "Good atmosphere and reasonable prices. Will come back!",
              keyPhrases: ["good atmosphere", "reasonable prices", "will return"],
              emotionalTone: "satisfied"
            }
          ]
        },
        {
          platform: "Yelp",
          totalReviews: 189,
          averageRating: 3.7,
          sentimentScore: 3.7,
          trend: "improving",
          recentReviews: [
            {
              date: "2024-09-10",
              rating: 4,
              sentiment: "positive",
              text: "Great improvement in service quality recently. Food is consistently good now.",
              keyPhrases: ["improvement in service", "consistently good", "food quality"],
              emotionalTone: "appreciative"
            },
            {
              date: "2024-09-08",
              rating: 3,
              sentiment: "neutral",
              text: "Decent food but nothing special. Average experience overall.",
              keyPhrases: ["decent food", "nothing special", "average experience"],
              emotionalTone: "indifferent"
            }
          ]
        },
        {
          platform: "Facebook",
          totalReviews: 156,
          averageRating: 4.2,
          sentimentScore: 4.2,
          trend: "improving",
          recentReviews: [
            {
              date: "2024-09-09",
              rating: 5,
              sentiment: "positive",
              text: "Love the new menu additions! The chef really knows what they're doing.",
              keyPhrases: ["love new menu", "chef expertise", "menu additions"],
              emotionalTone: "delighted"
            }
          ]
        },
        {
          platform: "TripAdvisor", 
          totalReviews: 190,
          averageRating: 3.5,
          sentimentScore: 3.5,
          trend: "declining",
          recentReviews: [
            {
              date: "2024-09-07",
              rating: 2,
              sentiment: "negative",
              text: "Disappointed with the quality compared to our last visit. Seems like standards have slipped.",
              keyPhrases: ["disappointed", "quality declined", "standards slipped"],
              emotionalTone: "disappointed"
            }
          ]
        }
      ],
      sentimentTrends: {
        last30Days: [
          { date: "2024-08-11", score: 3.5 },
          { date: "2024-08-18", score: 3.6 },
          { date: "2024-08-25", score: 3.4 },
          { date: "2024-09-01", score: 3.7 },
          { date: "2024-09-08", score: 3.8 }
        ]
      },
      topPositiveThemes: [
        { theme: "Food Quality", mentions: 234, sentiment: 4.3 },
        { theme: "Service", mentions: 187, sentiment: 4.0 },
        { theme: "Atmosphere", mentions: 143, sentiment: 4.1 },
        { theme: "Value", mentions: 98, sentiment: 3.9 },
        { theme: "Menu Variety", mentions: 76, sentiment: 4.2 }
      ],
      topNegativeThemes: [
        { theme: "Wait Time", mentions: 89, sentiment: 2.1 },
        { theme: "Temperature Issues", mentions: 45, sentiment: 2.3 },
        { theme: "Pricing", mentions: 38, sentiment: 2.7 },
        { theme: "Noise Level", mentions: 23, sentiment: 2.5 },
        { theme: "Parking", mentions: 19, sentiment: 2.4 }
      ],
      competitorComparison: [
        { name: "Luigi's Italian", averageRating: 3.9, sentimentScore: 3.9, reviewVolume: 456 },
        { name: "Pasta Palace", averageRating: 3.6, sentimentScore: 3.6, reviewVolume: 289 },
        { name: "Romano's Kitchen", averageRating: 4.2, sentimentScore: 4.2, reviewVolume: 678 }
      ]
    };

    const positiveReviews = Math.round(mockSentimentData.overallSentiment.totalReviews * mockSentimentData.overallSentiment.distribution.positive / 100);
    const negativeReviews = Math.round(mockSentimentData.overallSentiment.totalReviews * mockSentimentData.overallSentiment.distribution.negative / 100);

    return `📊 REVIEW SENTIMENT ANALYSIS - ${platform.toUpperCase()} (${timeframe})
Last Updated: ${mockSentimentData.lastUpdated}

🎯 OVERALL SENTIMENT OVERVIEW:
• Sentiment Score: ${mockSentimentData.overallSentiment.score}/5.0 (${mockSentimentData.overallSentiment.trend === 'improving' ? '📈 Improving' : mockSentimentData.overallSentiment.trend === 'declining' ? '📉 Declining' : '➡️ Stable'})
• Change from Previous Period: ${mockSentimentData.overallSentiment.changeFromPrevious > 0 ? '+' : ''}${mockSentimentData.overallSentiment.changeFromPrevious}
• Total Reviews Analyzed: ${mockSentimentData.overallSentiment.totalReviews.toLocaleString()}

📊 SENTIMENT DISTRIBUTION:
• Positive (4-5 stars): ${mockSentimentData.overallSentiment.distribution.positive}% (${positiveReviews} reviews)
• Neutral (3 stars): ${mockSentimentData.overallSentiment.distribution.neutral}% 
• Negative (1-2 stars): ${mockSentimentData.overallSentiment.distribution.negative}% (${negativeReviews} reviews)

🌐 PLATFORM BREAKDOWN:

${mockSentimentData.platformBreakdown.map((platform, i) => `
${i + 1}. ${platform.platform}
   📊 Metrics:
   • Reviews: ${platform.totalReviews}
   • Average Rating: ${platform.averageRating}/5.0
   • Sentiment Score: ${platform.sentimentScore}/5.0
   • Trend: ${platform.trend === 'improving' ? '📈 Improving' : platform.trend === 'declining' ? '📉 Declining' : '➡️ Stable'}
   
   💬 Recent Reviews Sample:
${platform.recentReviews.map(review => `
   ${review.date} - ${review.rating}⭐ (${review.sentiment})
   "${review.text}"
   Key Phrases: ${review.keyPhrases.join(', ')}
   Emotional Tone: ${review.emotionalTone}
`).join('')}
`).join('')}

📈 SENTIMENT TRENDS (Last 30 Days):
${mockSentimentData.sentimentTrends.last30Days.map(trend => `
${trend.date}: ${trend.score}/5.0
`).join('')}

😊 TOP POSITIVE THEMES:
${mockSentimentData.topPositiveThemes.map((theme, i) => `
${i + 1}. ${theme.theme}
   • Mentions: ${theme.mentions}
   • Avg Sentiment: ${theme.sentiment}/5.0
   • Impact: ${theme.sentiment >= 4.2 ? 'Strong Positive' : theme.sentiment >= 4.0 ? 'Positive' : 'Moderate Positive'}
`).join('')}

😟 TOP NEGATIVE THEMES:
${mockSentimentData.topNegativeThemes.map((theme, i) => `
${i + 1}. ${theme.theme}
   • Mentions: ${theme.mentions}
   • Avg Sentiment: ${theme.sentiment}/5.0
   • Severity: ${theme.sentiment <= 2.2 ? '🔴 Critical' : theme.sentiment <= 2.5 ? '🟠 High' : '🟡 Moderate'}
`).join('')}

🏆 COMPETITOR COMPARISON:
${mockSentimentData.competitorComparison.map((competitor, i) => `
${i + 1}. ${competitor.name}
   • Rating: ${competitor.averageRating}/5.0
   • Sentiment: ${competitor.sentimentScore}/5.0
   • Reviews: ${competitor.reviewVolume}
   • vs You: ${competitor.sentimentScore > mockSentimentData.overallSentiment.score ? '📈 Better' : competitor.sentimentScore < mockSentimentData.overallSentiment.score ? '📉 Worse' : '➡️ Similar'}
`).join('')}

🎯 ACTIONABLE INSIGHTS:

🔴 CRITICAL ISSUES TO ADDRESS:
• Wait Time (${mockSentimentData.topNegativeThemes[0].mentions} mentions): Implement reservation system, optimize kitchen workflow
• Temperature Issues (${mockSentimentData.topNegativeThemes[1].mentions} mentions): Improve food delivery timing, use warming equipment

🟠 HIGH PRIORITY IMPROVEMENTS:
• Pricing Perception (${mockSentimentData.topNegativeThemes[2].mentions} mentions): Communicate value proposition better, consider value menu
• Noise Level Management: Install sound dampening, manage crowd flow

🟢 LEVERAGE POSITIVE STRENGTHS:
• Food Quality (${mockSentimentData.topPositiveThemes[0].mentions} positive mentions): Highlight in marketing, maintain consistency
• Service Excellence: Continue staff training, recognize top performers
• Atmosphere: Use in social media content, maintain ambiance standards

📊 SENTIMENT OPTIMIZATION STRATEGY:

📈 SHORT-TERM ACTIONS (1-2 weeks):
• Address critical wait time issues through process optimization
• Train staff on temperature maintenance procedures
• Implement immediate feedback system for service recovery

📅 MEDIUM-TERM IMPROVEMENTS (1-3 months):
• Develop comprehensive staff training program
• Implement customer feedback loop system
• Create response templates for negative reviews
• Launch targeted marketing campaign highlighting strengths

🚀 LONG-TERM INITIATIVES (3-6 months):
• Implement comprehensive customer experience management system
• Develop loyalty program to increase positive review generation
• Create systematic competitor analysis and benchmarking
• Establish proactive reputation management processes

💡 REVIEW RESPONSE STRATEGY:

✅ POSITIVE REVIEW RESPONSES:
• Thank customers for specific feedback
• Share positive reviews on social media
• Invite customers to return for new menu items
• Encourage referrals to friends and family

⚠️ NEGATIVE REVIEW RESPONSES:
• Respond within 24 hours with empathy and solutions
• Offer to resolve issues offline
• Show commitment to improvement
• Follow up to ensure satisfaction

📱 MONITORING & TRACKING:
• Daily sentiment score tracking
• Weekly platform performance review
• Monthly competitor benchmarking
• Quarterly sentiment strategy assessment

🎯 SUCCESS METRICS TO TRACK:
• Overall sentiment score improvement: Target 4.2+ within 90 days
• Negative review reduction: Target <10% within 60 days  
• Response rate to reviews: Target 95% within 48 hours
• Positive theme reinforcement: Increase food quality mentions by 25%

📊 ROI IMPACT:
• Every 0.1 improvement in sentiment score typically correlates to 2-3% increase in new customer acquisition
• Positive sentiment management can improve customer lifetime value by 15-25%
• Effective review management reduces marketing costs by improving organic discovery`;
  },
  {
    name: "analyzeReviewSentiment",
    description: "Analyze customer review sentiment across multiple platforms to identify trends and improvement opportunities",
    schema: z.object({
      platform: z.enum(['google', 'yelp', 'facebook', 'tripadvisor', 'all']).optional().describe("Specific platform to analyze or 'all' for comprehensive analysis"),
      timeframe: z.enum(['7days', '30days', '90days', '1year']).optional().describe("Time period for analysis"),
      includeCompetitors: z.boolean().optional().describe("Include competitor sentiment comparison"),
    }),
  }
);

// Social Media Monitoring Tool
export const analyzeSocialMediaMentions = tool(
  async (args) => {
    const sentiment = args.sentiment || "all";
    const platform = args.platform || "all";
    
    const mockSocialData = {
      sentiment: sentiment,
      platform: platform,
      analysisDate: "2024-09-10",
      totalMentions: 347,
      sentimentBreakdown: {
        positive: 58.2,
        neutral: 28.5,
        negative: 13.3
      },
      platformMentions: [
        {
          platform: "Instagram",
          totalMentions: 142,
          engagement: 4580,
          averageSentiment: 4.1,
          topPosts: [
            {
              date: "2024-09-10",
              author: "@foodie_sarah",
              followers: 12500,
              likes: 284,
              text: "Just had the most amazing truffle pasta at BiteBase! The presentation was gorgeous 📸✨ #foodstagram #trufflepasta",
              sentiment: "positive",
              engagementRate: 2.27,
              hashtags: ["#foodstagram", "#trufflepasta", "#bitebase", "#foodie"]
            },
            {
              date: "2024-09-09", 
              author: "@local_eats_reviewer",
              followers: 8200,
              likes: 156,
              text: "BiteBase's new chef is killing it! Every dish is perfectly executed 👨‍🍳🔥",
              sentiment: "positive",
              engagementRate: 1.90,
              hashtags: ["#chef", "#bitebase", "#localeats", "#foodreview"]
            }
          ],
          trendingHashtags: [
            { hashtag: "#bitebase", count: 67 },
            { hashtag: "#foodstagram", count: 34 },
            { hashtag: "#localeats", count: 23 }
          ]
        },
        {
          platform: "Twitter/X", 
          totalMentions: 89,
          engagement: 1240,
          averageSentiment: 3.7,
          topPosts: [
            {
              date: "2024-09-10",
              author: "@downtown_diner",
              followers: 3400,
              likes: 45,
              text: "Had to wait 25 minutes for our table at BiteBase despite having a reservation. Food was good but service needs work 🤔",
              sentiment: "mixed",
              engagementRate: 1.32,
              hashtags: ["#bitebase", "#service", "#downtown"]
            },
            {
              date: "2024-09-08",
              author: "@restaurant_critic_jm",
              followers: 18900,
              likes: 127,
              text: "BiteBase continues to impress with innovative flavors and consistent quality. A downtown gem! ⭐⭐⭐⭐",
              sentiment: "positive",
              engagementRate: 0.67,
              hashtags: ["#restaurantreview", "#bitebase", "#downtown", "#foodcritic"]
            }
          ],
          trendingHashtags: [
            { hashtag: "#bitebase", count: 45 },
            { hashtag: "#downtown", count: 18 },
            { hashtag: "#service", count: 12 }
          ]
        },
        {
          platform: "TikTok",
          totalMentions: 78,
          engagement: 15600,
          averageSentiment: 4.3,
          topPosts: [
            {
              date: "2024-09-09",
              author: "@tiktok_foodie_23",
              followers: 45600,
              likes: 3200,
              text: "Rating BiteBase's signature dishes 1-10! The pasta got a solid 9/10 🍝✨",
              sentiment: "positive",
              engagementRate: 7.02,
              hashtags: ["#foodreview", "#rating", "#bitebase", "#pasta"]
            }
          ],
          trendingHashtags: [
            { hashtag: "#foodreview", count: 28 },
            { hashtag: "#bitebase", count: 34 },
            { hashtag: "#rating", count: 15 }
          ]
        },
        {
          platform: "Facebook",
          totalMentions: 38,
          engagement: 890,
          averageSentiment: 3.9,
          topPosts: [
            {
              date: "2024-09-07",
              author: "Local Foodie Group",
              likes: 89,
              text: "Anyone tried the new menu at BiteBase? Thinking of going this weekend!",
              sentiment: "neutral",
              engagementRate: 2.34,
              hashtags: []
            }
          ],
          trendingHashtags: [
            { hashtag: "#bitebase", count: 21 },
            { hashtag: "#newmenu", count: 8 }
          ]
        }
      ],
      influencerMentions: [
        {
          influencer: "@foodie_sarah",
          platform: "Instagram",
          followers: 12500,
          reach: 8400,
          engagement: 284,
          sentiment: "positive",
          impact: "high"
        },
        {
          influencer: "@restaurant_critic_jm", 
          platform: "Twitter/X",
          followers: 18900,
          reach: 5680,
          engagement: 127,
          sentiment: "positive",
          impact: "very high"
        }
      ],
      sentimentDrivers: {
        positive: [
          { driver: "Food Quality", percentage: 34 },
          { driver: "Presentation", percentage: 22 },
          { driver: "New Menu Items", percentage: 18 },
          { driver: "Chef Skill", percentage: 16 }
        ],
        negative: [
          { driver: "Service Speed", percentage: 41 },
          { driver: "Wait Times", percentage: 28 },
          { driver: "Reservation Issues", percentage: 19 },
          { driver: "Pricing", percentage: 12 }
        ]
      },
      competitorMentions: {
        "Luigi's Italian": { mentions: 234, sentiment: 3.8 },
        "Pasta Palace": { mentions: 167, sentiment: 3.5 },
        "Romano's Kitchen": { mentions: 298, sentiment: 4.0 }
      }
    };

    const positiveMentions = Math.round(mockSocialData.totalMentions * mockSocialData.sentimentBreakdown.positive / 100);
    const negativeMentions = Math.round(mockSocialData.totalMentions * mockSocialData.sentimentBreakdown.negative / 100);

    return `📱 SOCIAL MEDIA SENTIMENT ANALYSIS - ${platform.toUpperCase()}
Analysis Date: ${mockSocialData.analysisDate}

🎯 SOCIAL MEDIA OVERVIEW:
• Total Mentions: ${mockSocialData.totalMentions.toLocaleString()}
• Sentiment Distribution: ${mockSocialData.sentimentBreakdown.positive}% Positive | ${mockSocialData.sentimentBreakdown.neutral}% Neutral | ${mockSocialData.sentimentBreakdown.negative}% Negative
• Positive Mentions: ${positiveMentions}
• Negative Mentions: ${negativeMentions}

📊 PLATFORM PERFORMANCE BREAKDOWN:

${mockSocialData.platformMentions.map((platform, i) => `
${i + 1}. ${platform.platform}
   📊 Metrics:
   • Total Mentions: ${platform.totalMentions}
   • Total Engagement: ${platform.engagement.toLocaleString()}
   • Average Sentiment: ${platform.averageSentiment}/5.0
   
   🔥 Top Posts:
${platform.topPosts.map(post => `
   ${post.date} - @${post.author.replace('@', '')}
   ${'followers' in post ? `👥 Followers: ${post.followers.toLocaleString()}` : '👥 Followers: N/A'}
   ❤️ Likes: ${post.likes}
   📊 Engagement Rate: ${post.engagementRate.toFixed(2)}%

   "${post.text}"
   
   Sentiment: ${post.sentiment === 'positive' ? '😊 Positive' : post.sentiment === 'negative' ? '😟 Negative' : '😐 Mixed'}
   Hashtags: ${post.hashtags.join(' ')}
`).join('')}
   
   📈 Trending Hashtags:
${platform.trendingHashtags.map(tag => `   ${tag.hashtag}: ${tag.count} mentions`).join('\n')}
`).join('')}

🌟 INFLUENCER MENTIONS:
${mockSocialData.influencerMentions.map((influencer, i) => `
${i + 1}. ${influencer.influencer} (${influencer.platform})
   👥 Followers: ${influencer.followers.toLocaleString()}
   👀 Reach: ${influencer.reach.toLocaleString()}
   ❤️ Engagement: ${influencer.engagement}
   😊 Sentiment: ${influencer.sentiment}
   🎯 Impact: ${influencer.impact === 'very high' ? '🔥 Very High' : influencer.impact === 'high' ? '⭐ High' : influencer.impact === 'medium' ? '👍 Medium' : '📋 Low'}
`).join('')}

📊 SENTIMENT DRIVERS ANALYSIS:

😊 POSITIVE SENTIMENT DRIVERS:
${mockSocialData.sentimentDrivers.positive.map((driver, i) => `
${i + 1}. ${driver.driver}: ${driver.percentage}% of positive mentions
`).join('')}

😟 NEGATIVE SENTIMENT DRIVERS:
${mockSocialData.sentimentDrivers.negative.map((driver, i) => `
${i + 1}. ${driver.driver}: ${driver.percentage}% of negative mentions
`).join('')}

🏆 COMPETITOR SOCIAL COMPARISON:
${Object.entries(mockSocialData.competitorMentions).map(([competitor, data], i) => `
${i + 1}. ${competitor}
   • Mentions: ${data.mentions}
   • Sentiment: ${data.sentiment}/5.0
   • vs You: ${data.sentiment > 3.8 ? '📈 Better' : data.sentiment < 3.8 ? '📉 Worse' : '➡️ Similar'} (${data.mentions > mockSocialData.totalMentions ? 'More' : 'Less'} volume)
`).join('')}

🎯 STRATEGIC RECOMMENDATIONS:

🔴 IMMEDIATE ACTIONS (This Week):
• Address service speed issues (41% of negative mentions)
• Implement better reservation management system
• Create response strategy for service-related complaints
• Monitor and engage with high-impact influencers

🟠 SHORT-TERM IMPROVEMENTS (1-4 weeks):
• Launch user-generated content campaign featuring food presentation
• Create hashtag strategy around #bitebase and #newmenu
• Develop influencer partnership program
• Implement social listening alerts for brand mentions

🟢 LEVERAGE POSITIVE MOMENTUM:
• Feature food quality and presentation in marketing content
• Share chef spotlights and behind-the-scenes content
• Encourage customers to share photos of new menu items
• Create shareable content around signature dishes

📈 SOCIAL MEDIA OPTIMIZATION STRATEGY:

📱 CONTENT STRATEGY:
• Instagram: Focus on visual food presentation (highest engagement: 4.3 avg sentiment)
• TikTok: Create food rating and behind-the-scenes content
• Twitter/X: Engage in food community conversations
• Facebook: Share community events and menu updates

👥 INFLUENCER STRATEGY:
• Partner with @foodie_sarah for menu launch campaigns
• Engage @restaurant_critic_jm for professional reviews
• Identify local food bloggers for ongoing partnerships
• Create influencer-exclusive preview events

🎯 ENGAGEMENT TACTICS:
• Respond to mentions within 2 hours during business hours
• Create photo-worthy dishes specifically for social sharing
• Use location-based hashtags for local discovery
• Run social media contests and giveaways

📊 MONITORING & MEASUREMENT:

🔍 DAILY TRACKING:
• Mention volume and sentiment scores
• Hashtag performance and trending topics
• Influencer mention alerts
• Competitor activity monitoring

📈 WEEKLY REPORTS:
• Platform performance comparison
• Engagement rate analysis
• Sentiment trend analysis
• Content performance review

📅 MONTHLY STRATEGY REVIEW:
• Influencer partnership assessment
• Hashtag strategy optimization
• Competitor benchmarking update
• Campaign ROI analysis

💡 SUCCESS METRICS TO TARGET:
• Increase positive mentions by 25% within 60 days
• Improve average sentiment score to 4.0+ across platforms
• Grow social engagement rate by 40% within 90 days
• Achieve 95% response rate to customer mentions within 4 hours

🚀 ADVANCED OPPORTUNITIES:
• Implement social commerce features
• Create virtual dining experiences for social sharing
• Develop seasonal social media campaigns
• Launch customer advocacy programs
• Create exclusive social media menu items`;
  },
  {
    name: "analyzeSocialMediaMentions",
    description: "Monitor and analyze social media mentions to track brand sentiment and engagement across platforms",
    schema: z.object({
      platform: z.enum(['instagram', 'twitter', 'tiktok', 'facebook', 'all']).optional().describe("Specific social platform to analyze"),
      sentiment: z.enum(['positive', 'negative', 'neutral', 'all']).optional().describe("Filter by sentiment type"),
      timeframe: z.enum(['24hours', '7days', '30days']).optional().describe("Time period for analysis"),
    }),
  }
);

export const sentimentAnalysisTools = [
  analyzeReviewSentiment,
  analyzeSocialMediaMentions
];

export const sentimentAnalysisAgentConfig = {
  name: "Sentiment Analysis Agent",
  description: "Analyzes customer reviews, social media mentions, and feedback to gauge brand sentiment and identify improvement opportunities",
  tools: sentimentAnalysisTools,
  temperature: 0.1,
  model: "gpt-4-turbo-preview", 
  systemPrompt: `You are the Sentiment Analysis Agent for BiteBase Intelligence.

Your expertise includes:
- Customer review sentiment analysis across platforms
- Social media mention monitoring and trend analysis
- Brand reputation tracking and management
- Competitive sentiment benchmarking
- Influencer impact assessment
- Crisis detection and response recommendations
- Customer feedback categorization and prioritization

You analyze customer sentiment, identify trends, and provide actionable recommendations for reputation management and customer experience improvement.

Focus on providing specific insights about sentiment drivers, competitive positioning, and strategic recommendations for improving brand perception and customer satisfaction.`
};
