/**
 * Menu Performance Agent - Product Module
 * 
 * Analyzes sales data to identify top-selling and low-performing dishes.
 * Part of the BiteBase Intelligence agentic AI framework.
 */

import { z } from "zod";
import { tool } from "@langchain/core/tools";

// Menu Performance Analysis Tool
export const analyzeMenuPerformance = tool(
  async (args) => {
    // Simulate comprehensive menu performance analysis
    const mockMenuItems = [
      {
        name: "Classic Margherita Pizza",
        category: "Main Course",
        unitsSold: 342,
        revenue: 4104.00,
        avgRating: 4.6,
        profitMargin: 68,
        trend: "up",
        seasonality: "stable"
      },
      {
        name: "Truffle Mushroom Risotto",
        category: "Main Course", 
        unitsSold: 128,
        revenue: 2560.00,
        avgRating: 4.8,
        profitMargin: 72,
        trend: "up",
        seasonality: "high"
      },
      {
        name: "Caesar Salad",
        category: "Appetizer",
        unitsSold: 267,
        revenue: 2670.00,
        avgRating: 4.2,
        profitMargin: 75,
        trend: "stable",
        seasonality: "stable"
      },
      {
        name: "Chocolate Lava Cake",
        category: "Dessert",
        unitsSold: 89,
        revenue: 712.00,
        avgRating: 4.7,
        profitMargin: 78,
        trend: "down",
        seasonality: "low"
      },
      {
        name: "Grilled Salmon",
        category: "Main Course",
        unitsSold: 156,
        revenue: 3744.00,
        avgRating: 4.4,
        profitMargin: 58,
        trend: "stable",
        seasonality: "medium"
      }
    ];

    const topPerformers = mockMenuItems
      .sort((a, b) => (b.unitsSold * b.profitMargin) - (a.unitsSold * a.profitMargin))
      .slice(0, 3);

    const underPerformers = mockMenuItems
      .sort((a, b) => (a.unitsSold * a.profitMargin) - (b.unitsSold * b.profitMargin))
      .slice(0, 2);

    const totalRevenue = mockMenuItems.reduce((sum, item) => sum + item.revenue, 0);
    const avgProfitMargin = mockMenuItems.reduce((sum, item) => sum + item.profitMargin, 0) / mockMenuItems.length;

    return `📊 MENU PERFORMANCE ANALYSIS - ${args.period}

🎯 KEY METRICS:
• Total Menu Items Analyzed: ${mockMenuItems.length}
• Total Revenue: $${totalRevenue.toLocaleString()}
• Average Profit Margin: ${avgProfitMargin.toFixed(1)}%
• Menu Velocity Score: ${Math.floor(Math.random() * 20) + 75}/100

🌟 TOP PERFORMERS (Revenue × Profit Impact):
${topPerformers.map((item, i) => `
${i + 1}. ${item.name} (${item.category})
   • Units Sold: ${item.unitsSold} | Revenue: $${item.revenue.toLocaleString()}
   • Profit Margin: ${item.profitMargin}% | Rating: ${item.avgRating}⭐
   • Trend: ${item.trend === 'up' ? '📈 Growing' : item.trend === 'stable' ? '➡️ Stable' : '📉 Declining'}
`).join('')}

⚠️ UNDERPERFORMERS:
${underPerformers.map((item, i) => `
${i + 1}. ${item.name} (${item.category})
   • Units Sold: ${item.unitsSold} | Revenue: $${item.revenue.toLocaleString()}
   • Issues: ${item.unitsSold < 100 ? 'Low volume, ' : ''}${item.profitMargin < 60 ? 'Low margin' : 'Needs promotion'}
`).join('')}

💡 STRATEGIC RECOMMENDATIONS:
• Promote ${topPerformers[0].name} with special offers to drive volume
• Consider removing or redesigning ${underPerformers[0].name}
• Develop seasonal variations of high-performing items
• Optimize portion sizes for items with <65% profit margin
• A/B test new descriptions for underperforming items

📈 GROWTH OPPORTUNITIES:
• Menu engineering potential: $${Math.floor(Math.random() * 5000) + 2000}/month
• Cross-selling opportunities identified
• Seasonal menu optimization recommended`;
  },
  {
    name: "analyzeMenuPerformance",
    description: "Analyze sales data to identify top-selling and low-performing dishes with detailed insights",
    schema: z.object({
      period: z.string().describe("Analysis period (e.g., 'last 30 days', 'Q1 2024')"),
      includeSeasonality: z.boolean().optional().describe("Include seasonal trend analysis"),
      focusCategory: z.string().optional().describe("Focus on specific menu category"),
    }),
  }
);

// Cost Analysis Tool
export const analyzeFoodCosts = tool(
  async (args) => {
    const mockCostAnalysis = {
      items: [
        { name: "Margherita Pizza", foodCost: 3.50, sellPrice: 12.00, margin: 70.8 },
        { name: "Caesar Salad", foodCost: 2.25, sellPrice: 10.00, margin: 77.5 },
        { name: "Truffle Risotto", foodCost: 5.60, sellPrice: 20.00, margin: 72.0 },
        { name: "Grilled Salmon", foodCost: 10.50, sellPrice: 24.00, margin: 56.3 }
      ]
    };

    const avgMargin = mockCostAnalysis.items.reduce((sum, item) => sum + item.margin, 0) / mockCostAnalysis.items.length;

    return `💰 FOOD COST & PROFITABILITY ANALYSIS

📊 COST BREAKDOWN BY ITEM:
${mockCostAnalysis.items.map(item => `
• ${item.name}
  Food Cost: $${item.foodCost} | Sell Price: $${item.sellPrice}
  Profit Margin: ${item.margin.toFixed(1)}% ${item.margin > avgMargin ? '✅' : '⚠️'}
`).join('')}

📈 KEY INSIGHTS:
• Average Food Cost Percentage: ${(100 - avgMargin).toFixed(1)}%
• Target Food Cost Range: 25-32% (Industry Standard)
• Current Performance: ${avgMargin > 70 ? 'Excellent' : avgMargin > 60 ? 'Good' : 'Needs Improvement'}

💡 OPTIMIZATION RECOMMENDATIONS:
• Review portion sizes for items with <60% margin
• Negotiate better pricing with suppliers
• Consider ingredient substitutions for high-cost items
• Implement waste tracking for better cost control`;
  },
  {
    name: "analyzeFoodCosts",
    description: "Calculate food cost vs. profitability for each menu item",
    schema: z.object({
      includeWasteAnalysis: z.boolean().optional().describe("Include food waste analysis"),
      targetMargin: z.number().optional().describe("Target profit margin percentage"),
    }),
  }
);

export const menuPerformanceTools = [
  analyzeMenuPerformance,
  analyzeFoodCosts
];

export const menuPerformanceAgentConfig = {
  name: "Menu Performance Agent",
  description: "Analyzes sales data to identify top-selling and low-performing dishes",
  tools: menuPerformanceTools,
  temperature: 0.1,
  model: "gpt-4-turbo-preview",
  systemPrompt: `You are the Menu Performance Agent for BiteBase Intelligence.

Your expertise includes:
- Menu item sales analysis and performance tracking
- Food cost analysis and profit margin optimization  
- Menu engineering and pricing strategy
- Seasonal trend analysis and demand forecasting
- Cross-selling and upselling opportunity identification

You analyze restaurant menu performance data to provide actionable insights that help optimize profitability, eliminate underperforming items, and identify growth opportunities.

Focus on delivering quantitative analysis with specific, implementable recommendations for menu optimization.`
};
