/**
 * Cost & Profitability Agent - Product Module
 * 
 * Calculates food cost vs. profitability for each menu item and provides
 * cost optimization recommendations.
 */

import { z } from "zod";
import { tool } from "@langchain/core/tools";

// Comprehensive Cost Analysis Tool
export const analyzeProfitability = tool(
  async (args) => {
    const mockProfitabilityData = {
      menuItems: [
        {
          item: "Signature Burger",
          ingredients: [
            { name: "Beef Patty", cost: 3.50, supplier: "Local Farm Co" },
            { name: "Artisan Bun", cost: 0.75, supplier: "Bakery Plus" },
            { name: "Cheese", cost: 0.45, supplier: "Dairy Direct" },
            { name: "Vegetables", cost: 0.80, supplier: "Fresh Greens" }
          ],
          totalFoodCost: 5.50,
          laborCost: 2.25,
          totalCost: 7.75,
          sellPrice: 16.00,
          grossProfit: 8.25,
          profitMargin: 51.6,
          volume: 285,
          totalProfit: 2351.25
        },
        {
          item: "Mediterranean Bowl",
          ingredients: [
            { name: "<PERSON><PERSON>oa", cost: 1.25, supplier: "Grain Source" },
            { name: "Feta Cheese", cost: 1.10, supplier: "Greek Imports" },
            { name: "Olives", cost: 0.65, supplier: "Olive Grove" },
            { name: "Vegetables", cost: 1.20, supplier: "Fresh Greens" }
          ],
          totalFoodCost: 4.20,
          laborCost: 1.80,
          totalCost: 6.00,
          sellPrice: 14.00,
          grossProfit: 8.00,
          profitMargin: 57.1,
          volume: 198,
          totalProfit: 1584.00
        },
        {
          item: "Lobster Thermidor",
          ingredients: [
            { name: "Lobster Tail", cost: 12.50, supplier: "Coastal Seafood" },
            { name: "Cream", cost: 0.85, supplier: "Dairy Direct" },
            { name: "Cheese", cost: 0.95, supplier: "Dairy Direct" },
            { name: "Herbs", cost: 0.70, supplier: "Herb Garden" }
          ],
          totalFoodCost: 15.00,
          laborCost: 4.50,
          totalCost: 19.50,
          sellPrice: 42.00,
          grossProfit: 22.50,
          profitMargin: 53.6,
          volume: 67,
          totalProfit: 1507.50
        }
      ]
    };

    const totalProfit = mockProfitabilityData.menuItems.reduce((sum, item) => sum + item.totalProfit, 0);
    const avgProfitMargin = mockProfitabilityData.menuItems.reduce((sum, item) => sum + item.profitMargin, 0) / mockProfitabilityData.menuItems.length;

    return `💰 COMPREHENSIVE PROFITABILITY ANALYSIS

🎯 EXECUTIVE SUMMARY:
• Total Items Analyzed: ${mockProfitabilityData.menuItems.length}
• Combined Profit Generated: $${totalProfit.toLocaleString()}
• Average Profit Margin: ${avgProfitMargin.toFixed(1)}%
• Cost Structure Health: ${avgProfitMargin > 55 ? '🟢 Excellent' : avgProfitMargin > 45 ? '🟡 Good' : '🔴 Needs Attention'}

📊 DETAILED ITEM BREAKDOWN:
${mockProfitabilityData.menuItems.map((item, i) => `
${i + 1}. ${item.item}
   💵 Financial Metrics:
   • Food Cost: $${item.totalFoodCost} | Labor: $${item.laborCost}
   • Total Cost: $${item.totalCost} | Sell Price: $${item.sellPrice}
   • Gross Profit: $${item.grossProfit} (${item.profitMargin.toFixed(1)}%)
   • Monthly Volume: ${item.volume} units
   • Total Contribution: $${item.totalProfit.toLocaleString()}

   🔍 Cost Breakdown:
${item.ingredients.map(ing => `   • ${ing.name}: $${ing.cost} (${ing.supplier})`).join('\n')}
`).join('')}

📈 PROFIT OPTIMIZATION OPPORTUNITIES:

🎯 HIGH IMPACT ACTIONS:
• Negotiate bulk pricing with Dairy Direct (appears in multiple items)
• Review portion sizes for items with 45-50% margin
• Consider premium pricing for high-demand, low-margin items

💡 SUPPLIER OPTIMIZATION:
• Consolidate orders with Fresh Greens for better rates
• Explore alternative suppliers for high-cost ingredients
• Implement vendor performance scorecards

🔄 MENU ENGINEERING RECOMMENDATIONS:
• Promote high-margin, high-volume items (Stars)
• Redesign or discontinue low-margin items
• Bundle complementary items to increase average ticket

📊 FINANCIAL PROJECTIONS:
• Potential monthly profit increase: $${Math.floor(Math.random() * 1500) + 800}
• ROI on cost optimization: ${Math.floor(Math.random() * 15) + 20}%
• Break-even improvement: ${Math.floor(Math.random() * 5) + 3} days faster`;
  },
  {
    name: "analyzeProfitability",
    description: "Comprehensive profitability analysis including ingredient costs, labor, and margin optimization",
    schema: z.object({
      includeSupplierAnalysis: z.boolean().optional().describe("Include detailed supplier cost analysis"),
      targetMarginThreshold: z.number().optional().describe("Minimum acceptable profit margin percentage"),
      period: z.string().optional().describe("Analysis period (default: current month)"),
    }),
  }
);

export const calculateBreakEven = tool(
  async (args) => {
    const fixedCosts = {
      rent: 8500,
      utilities: 1200,
      insurance: 450,
      licenses: 200,
      salaries: 15000,
      other: 800
    };

    const totalFixedCosts = Object.values(fixedCosts).reduce((sum, cost) => sum + cost, 0);
    const avgVariableCostPercentage = 35; // 35% of revenue
    const contributionMargin = 100 - avgVariableCostPercentage;
    const breakEvenRevenue = (totalFixedCosts * 100) / contributionMargin;
    const avgTicket = args.averageTicket || 28;
    const breakEvenCustomers = Math.ceil(breakEvenRevenue / avgTicket);
    const dailyBreakEven = Math.ceil(breakEvenCustomers / 30);

    return `🎯 BREAK-EVEN ANALYSIS

💰 MONTHLY FIXED COSTS:
• Rent: $${fixedCosts.rent.toLocaleString()}
• Salaries: $${fixedCosts.salaries.toLocaleString()}
• Utilities: $${fixedCosts.utilities.toLocaleString()}
• Insurance: $${fixedCosts.insurance.toLocaleString()}
• Licenses: $${fixedCosts.licenses.toLocaleString()}
• Other: $${fixedCosts.other.toLocaleString()}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━
• Total Fixed Costs: $${totalFixedCosts.toLocaleString()}/month

📊 BREAK-EVEN CALCULATIONS:
• Variable Cost Percentage: ${avgVariableCostPercentage}%
• Contribution Margin: ${contributionMargin}%
• Average Ticket Size: $${avgTicket}

🎯 BREAK-EVEN TARGETS:
• Monthly Revenue: $${breakEvenRevenue.toLocaleString()}
• Monthly Customers: ${breakEvenCustomers.toLocaleString()}
• Daily Customers: ${dailyBreakEven} customers/day

📈 SENSITIVITY ANALYSIS:
• +10% Average Ticket → ${Math.floor(dailyBreakEven * 0.91)} customers/day
• +5% Efficiency → ${Math.floor(dailyBreakEven * 0.95)} customers/day
• -10% Fixed Costs → ${Math.floor(dailyBreakEven * 0.9)} customers/day

💡 OPTIMIZATION STRATEGIES:
• Focus on increasing average ticket size
• Reduce variable costs through supplier negotiations
• Optimize labor scheduling during low-volume periods
• Implement upselling training for staff`;
  },
  {
    name: "calculateBreakEven",
    description: "Calculate break-even points and provide sensitivity analysis",
    schema: z.object({
      averageTicket: z.number().optional().describe("Average customer ticket size"),
      customFixedCosts: z.object({}).optional().describe("Custom fixed cost structure"),
    }),
  }
);

export const costProfitabilityTools = [
  analyzeProfitability,
  calculateBreakEven
];

export const costProfitabilityAgentConfig = {
  name: "Cost & Profitability Agent",
  description: "Calculates food cost vs. profitability for each menu item and provides cost optimization recommendations",
  tools: costProfitabilityTools,
  temperature: 0.1,
  model: "gpt-4-turbo-preview",
  systemPrompt: `You are the Cost & Profitability Agent for BiteBase Intelligence.

Your expertise includes:
- Detailed ingredient and labor cost analysis
- Profit margin calculation and optimization
- Break-even analysis and financial modeling
- Supplier cost comparison and negotiation insights
- Menu pricing strategy and cost structure optimization

You analyze every aspect of restaurant profitability, from individual ingredient costs to overall financial performance, providing actionable recommendations for cost reduction and margin improvement.

Focus on delivering precise financial analysis with clear, implementable strategies for profitability enhancement.`
};
