{"name": "bitebase-intelligence-agent", "version": "1.0.0", "description": "BiteBase Intelligence Agentic AI System - Multi-agent framework for restaurant market research", "main": "src/agent.ts", "type": "module", "scripts": {"dev": "npx @langchain/langgraph-cli dev --port 8124 --no-browser", "dev:studio": "npx @langchain/langgraph-cli dev --port 8124", "build": "tsc", "start": "node dist/agent.js", "lint": "eslint src --ext .ts", "test": "jest", "clean": "rm -rf dist node_modules", "reinstall": "npm run clean && npm install"}, "keywords": ["restaurant", "market-research", "ai-agent", "langgraph", "copilotkit"], "author": "BiteBase Intelligence Team", "license": "MIT", "devDependencies": {"@types/html-to-text": "^9.0.4", "@types/node": "^22.9.0", "typescript": "^5.6.3"}, "dependencies": {"@copilotkit/sdk-js": "1.3.7", "@langchain/anthropic": "^0.3.8", "@langchain/core": "^0.3.18", "@langchain/google-genai": "^0.1.4", "@langchain/langgraph": "^0.2.57", "@langchain/langgraph-checkpoint": "^0.0.16", "@langchain/openai": "^0.3.14", "html-to-text": "^9.0.5", "uuid": "^10.0.0", "zod": "^3.24.4"}, "overrides": {"@copilotkit/sdk-js": "1.3.7", "@langchain/community@<0.3.3": ">=0.3.3"}, "pnpm": {"overrides": {"@copilotkit/sdk-js": "1.3.7", "@langchain/community@<0.3.3": ">=0.3.3"}}}