@echo off
REM BiteBase Intelligence - Complete Installation Script for Windows
REM This script ensures all dependencies are properly installed

echo 🚀 BiteBase Intelligence - Complete Installation
echo ==================================================

REM Check Node.js
node -v >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    exit /b 1
)

echo ✅ Node.js is installed
echo ✅ npm is available

REM Clean previous installations if requested
if "%1"=="--clean" (
    echo 🧹 Cleaning previous installations...
    if exist node_modules rmdir /s /q node_modules
    if exist package-lock.json del package-lock.json
    if exist agent\node_modules rmdir /s /q agent\node_modules
    if exist agent\package-lock.json del agent\package-lock.json
    echo ✅ Clean completed
)

REM Install root dependencies
echo 📦 Installing main dependencies...
call npm install --legacy-peer-deps
if errorlevel 1 (
    echo ❌ Failed to install main dependencies
    exit /b 1
)

REM Install agent dependencies
echo 📦 Installing agent dependencies...
cd agent
call npm install --legacy-peer-deps
if errorlevel 1 (
    echo ❌ Failed to install agent dependencies
    cd ..
    exit /b 1
)
cd ..

REM Fix CopilotKit versions
echo 🔄 Ensuring correct CopilotKit versions...
call npm install @copilotkit/react-core@1.3.7 @copilotkit/react-ui@1.3.7 @copilotkit/runtime@1.3.7 @copilotkit/react-textarea@1.3.7 @copilotkit/runtime-client-gql@1.3.7 --legacy-peer-deps

REM Create .env.local template if it doesn't exist
if not exist .env.local (
    echo 📝 Creating .env.local template...
    (
        echo # BiteBase Intelligence Environment Variables
        echo # Copy this template and add your actual API keys
        echo.
        echo # AI Model API Keys
        echo ANTHROPIC_API_KEY=your_anthropic_key_here
        echo OPENAI_API_KEY=your_openai_key_here
        echo.
        echo # LangGraph Configuration
        echo LANGGRAPH_DEPLOYMENT_URL=http://localhost:8124
        echo.
        echo # Optional: LangSmith Tracing
        echo LANGSMITH_API_KEY=your_langsmith_key_here
        echo LANGSMITH_TRACING=false
        echo.
        echo # Optional: GitHub Token for Copilot API
        echo GITHUB_TOKEN=your_github_token_here
    ) > .env.local
    echo ✅ Created .env.local template - Please add your API keys
) else (
    echo ✅ .env.local already exists
)

echo.
echo 🎉 Installation completed successfully!
echo.
echo Next steps:
echo 1. Add your API keys to .env.local
echo 2. Run: npm run dev
echo 3. Open: http://localhost:3000
echo.
echo Available commands:
echo   npm run dev           - Start development servers
echo   npm run dev:ui        - Start only UI server
echo   npm run dev:agent     - Start only agent server
echo   npm run build         - Build for production
echo   npm run reinstall     - Clean and reinstall all deps
echo.
pause
