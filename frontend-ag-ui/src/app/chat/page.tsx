"use client";

import { useCopilotAction } from "@copilotkit/react-core";
import { Copilot<PERSON><PERSON>, CopilotKitCSSProperties } from "@copilotkit/react-ui";
import { useState, useEffect } from "react";
import { ArrowLeft, MapPin, BarChart3, Download } from "lucide-react";
import Link from "next/link";

// Market Research State interfaces
interface MarketData {
  population: number;
  medianIncome: number;
  demographics: Record<string, number>;
  businessCount: number;
}

interface CompetitorData {
  name: string;
  type: string;
  distance: number;
  rating: number;
  priceRange: string;
}

interface LocationAnalysis {
  score: number;
  factors: Record<string, number>;
  recommendations: string[];
}

interface FinancialProjections {
  startupCosts: number;
  monthlyExpenses: number;
  projectedRevenue: number;
  breakEvenMonths: number;
}

interface MapVisualization {
  center: [number, number];
  markers: Array<{ lat: number; lng: number; type: string; data: Record<string, unknown> }>;
}

interface MarketResearchState {
  targetLocation: string;
  businessType: string;
  budget: number;
  targetAudience: string;
  currentResearchPhase: string;
  marketData: MarketData | null;
  competitorAnalysis: CompetitorData[];
  locationAnalysis: LocationAnalysis | null;
  financialProjections: FinancialProjections | null;
  reportProgress: number;
  generatedInsights: string[];
  mapVisualization: MapVisualization | null;
}

export default function ChatPage() {
  const [initialQuery, setInitialQuery] = useState("");
  const [showVisualization, setShowVisualization] = useState(false);
  const [hasAutoStarted, setHasAutoStarted] = useState(false);

  // Load initial query from localStorage on mount
  useEffect(() => {
    const query = localStorage.getItem('market_research_query');
    console.log('Loading initial query from localStorage:', query);
    if (query) {
      setInitialQuery(query);
      // Clear the query from localStorage once loaded to avoid duplication
      localStorage.removeItem('market_research_query');
      console.log('Initial query set:', query);
    }
  }, []);

  // Auto-start conversation if initial query exists
  useEffect(() => {
    if (initialQuery && !hasAutoStarted) {
      setHasAutoStarted(true);
      // Small delay to ensure CopilotChat is fully initialized
      setTimeout(() => {
        // The initial query will be handled by the instructions prop
        console.log(`Auto-starting with query: ${initialQuery}`);
      }, 1000);
    }
  }, [initialQuery, hasAutoStarted]);

  // 🪁 Frontend Actions: Research Phase Management
  useCopilotAction({
    name: "updateResearchPhase",
    description: "Update the current research phase and UI to reflect progress",
    parameters: [{
      name: "phase",
      type: "string",
      description: "Current research phase: consultation, location-analysis, competitor-research, financial-modeling, report-generation",
      required: true,
    }, {
      name: "progress",
      type: "number",
      description: "Progress percentage (0-100)",
      required: true,
    }],
    handler({ phase, progress }: { phase: string; progress: number }) {
      setState(prevState => {
        if (!prevState) return prevState;
        return {
          ...prevState,
          currentResearchPhase: phase,
          reportProgress: progress
        };
      });
    },
  });

  // 🪁 Frontend Actions: Business Details Management  
  useCopilotAction({
    name: "updateBusinessDetails",
    description: "Update business details for the research project",
    parameters: [{
      name: "businessType",
      type: "string",
      description: "Type of business: cafe, restaurant, fast-food, fine-dining, etc.",
      required: true,
    }, {
      name: "targetLocation",
      type: "string",
      description: "Target location for the business",
      required: true,
    }, {
      name: "budget",
      type: "number",
      description: "Startup budget amount",
      required: false,
    }, {
      name: "targetAudience",
      type: "string",
      description: "Target customer demographic",
      required: false,
    }],
    handler({ businessType, targetLocation, budget, targetAudience }: { 
      businessType: string; 
      targetLocation: string; 
      budget?: number; 
      targetAudience?: string; 
    }) {
      setState(prevState => {
        if (!prevState) return prevState;
        return {
          ...prevState,
          businessType,
          targetLocation,
          budget: budget || prevState.budget,
          targetAudience: targetAudience || prevState.targetAudience
        };
      });
    },
  });

  // 🪁 Frontend Actions: Map Visualization Toggle
  useCopilotAction({
    name: "showMapVisualization",
    description: "Show or hide the map visualization panel with market data",
    parameters: [{
      name: "show",
      type: "boolean",
      description: "Whether to show the visualization",
      required: true,
    }, {
      name: "data",
      type: "object",
      description: "Visualization data to display",
      required: false,
    }],
    handler({ show, data }: { show: boolean; data?: MapVisualization }) {
      setShowVisualization(show);
      if (data) {
        setState(prevState => {
          if (!prevState) return prevState;
          return {
            ...prevState,
            mapVisualization: data
          };
        });
      }
    },
  });

  // 🪁 Frontend Actions: Add Research Insights
  useCopilotAction({
    name: "addResearchInsights",
    description: "Add new insights to the research findings",
    parameters: [{
      name: "insights",
      type: "array",
      description: "Array of insight strings to add",
      required: true,
    }],
    handler({ insights }: { insights: string[] }) {
      setState(prevState => {
        if (!prevState) return prevState;
        return {
          ...prevState,
          generatedInsights: [...prevState.generatedInsights, ...insights]
        };
      });
    },
  });

  // 🪁 Temporary: Use regular state until API keys are configured
  // const { state, setState } = useCoAgent<MarketResearchState>({
  //   name: "starterAgent",
  //   initialState: {
  //     targetLocation: "",
  //     businessType: "",
  //     budget: 0,
  //     targetAudience: "",
  //     currentResearchPhase: "consultation",
  //     marketData: null,
  //     competitorAnalysis: [],
  //     locationAnalysis: null,
  //     financialProjections: null,
  //     reportProgress: 0,
  //     generatedInsights: [],
  //     mapVisualization: null,
  //   },
  // });

  // Temporary state for demo purposes
  const [state, setState] = useState<MarketResearchState>({
    targetLocation: "",
    businessType: "",
    budget: 0,
    targetAudience: "",
    currentResearchPhase: "consultation",
    marketData: null,
    competitorAnalysis: [],
    locationAnalysis: null,
    financialProjections: null,
    reportProgress: 0,
    generatedInsights: [],
    mapVisualization: null,
  });

  return (
    <div className="h-screen flex bg-gray-50" style={{ "--copilot-kit-primary-color": "#2563eb" } as CopilotKitCSSProperties}>
      {/* Left Panel - Research Progress & Data */}
      <div className={`${showVisualization ? 'w-1/3' : 'w-80'} bg-white border-r border-gray-200 flex flex-col transition-all duration-300`}>
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <Link href="/" className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Home
          </Link>
          <h1 className="text-xl font-bold text-gray-900">Market Research</h1>
          <p className="text-sm text-gray-600 mt-1">AI-Powered Analysis</p>
        </div>

        {/* Research Progress */}
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold mb-3">Research Progress</h2>
          
          <div className="space-y-3">
            <div className="bg-gray-100 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${state.reportProgress}%` }}
              />
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Current Phase:</span>
              <span className="font-medium capitalize">{state.currentResearchPhase.replace('-', ' ')}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Progress:</span>
              <span className="font-medium">{state.reportProgress}%</span>
            </div>
          </div>
        </div>

        {/* Business Details */}
        {(state.businessType || state.targetLocation) && (
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold mb-3">Business Details</h2>
            <div className="space-y-3">
              {state.businessType && (
                <div className="flex items-center text-sm">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                  <span className="text-gray-600 mr-2">Type:</span>
                  <span className="font-medium capitalize">{state.businessType}</span>
                </div>
              )}
              {state.targetLocation && (
                <div className="flex items-center text-sm">
                  <MapPin className="w-4 h-4 text-gray-400 mr-2" />
                  <span className="text-gray-600 mr-2">Location:</span>
                  <span className="font-medium">{state.targetLocation}</span>
                </div>
              )}
              {state.budget > 0 && (
                <div className="flex items-center text-sm">
                  <div className="w-2 h-2 bg-green-600 rounded-full mr-3"></div>
                  <span className="text-gray-600 mr-2">Budget:</span>
                  <span className="font-medium">${state.budget.toLocaleString()}</span>
                </div>
              )}
              {state.targetAudience && (
                <div className="flex items-center text-sm">
                  <div className="w-2 h-2 bg-purple-600 rounded-full mr-3"></div>
                  <span className="text-gray-600 mr-2">Audience:</span>
                  <span className="font-medium">{state.targetAudience}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Key Insights */}
        {state.generatedInsights.length > 0 && (
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold mb-3">Key Insights</h2>
            <div className="space-y-2">
              {state.generatedInsights.slice(0, 4).map((insight, index) => (
                <div key={index} className="text-sm p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                  {insight}
                </div>
              ))}
              {state.generatedInsights.length > 4 && (
                <p className="text-xs text-gray-500">+{state.generatedInsights.length - 4} more insights...</p>
              )}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="p-6 mt-auto">
          <h2 className="text-lg font-semibold mb-3">Quick Actions</h2>
          <div className="space-y-2">
            <button 
              onClick={() => setShowVisualization(!showVisualization)}
              className="w-full flex items-center justify-between p-3 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm transition-colors"
            >
              <div className="flex items-center">
                <BarChart3 className="w-4 h-4 mr-2" />
                Map & Data View
              </div>
              <span className={`text-xs ${showVisualization ? 'text-green-600' : 'text-gray-500'}`}>
                {showVisualization ? 'ON' : 'OFF'}
              </span>
            </button>
            
            {state.reportProgress > 50 && (
              <button className="w-full flex items-center p-3 bg-blue-600 text-white hover:bg-blue-700 rounded-lg text-sm transition-colors">
                <Download className="w-4 h-4 mr-2" />
                Download Report
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Center - Chat Interface */}
      <div className="flex-1 relative">
        <CopilotChat
          className="w-full h-full"
          instructions={initialQuery ? 
            `� **STARTING MARKET RESEARCH SESSION**

You have a market research request: "${initialQuery}"

I'm MarketScope AI, your professional restaurant market research assistant. Let me immediately begin analyzing this opportunity for you.

**Current Status:** Ready to analyze
**Query:** ${initialQuery}

Please provide these key details so I can create a comprehensive analysis:

🏪 **Business Type**: What kind of restaurant/cafe are you planning? (e.g., casual dining, fast-casual, coffee shop, fine dining)

📍 **Location**: Where are you looking to open? (City, neighborhood, or specific address)

💰 **Budget Range**: What's your estimated startup budget?

🎯 **Target Market**: Who are your ideal customers? (demographics, dining preferences)

Once I have these details, I'll provide:
✅ Location analysis with demographics & foot traffic
✅ Competitive landscape assessment  
✅ Financial projections & break-even analysis
✅ Market opportunity scoring
✅ Professional feasibility report

**Let's get started - please share those details above and I'll begin the research immediately!**` :
            `👋 **Welcome to MarketScope AI Professional Market Research**

I'm your specialized restaurant & cafe market research assistant, helping entrepreneurs make data-driven decisions about new food business ventures.

**What I Provide:**
📍 **Location Intelligence** - Demographics, foot traffic, commercial viability
🏪 **Competitive Analysis** - Market gaps, competitor strengths/weaknesses  
💰 **Financial Modeling** - Startup costs, revenue forecasts, profitability
📊 **Market Insights** - Industry trends, consumer behavior, growth opportunities
📋 **Professional Reports** - Comprehensive feasibility studies

**To begin your market research, please tell me:**

🏪 **Business Concept**: What type of restaurant/cafe are you planning?
📍 **Target Location**: Where do you want to open?
💰 **Investment Budget**: What's your startup budget range?
🎯 **Target Customers**: Who is your ideal customer base?

I'll guide you through a comprehensive analysis process and provide actionable insights for your business decision!`
          }
        />
      </div>

      {/* Right Panel - Map & Data Visualization (Conditional) */}
      {showVisualization && (
        <div className="w-1/3 bg-white border-l border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold">Market Visualization</h2>
            <p className="text-sm text-gray-600">Real-time data analysis</p>
          </div>
          
          <div className="p-6">
            {/* Mock Map/Chart Area */}
            <div className="bg-gray-100 rounded-lg h-64 flex items-center justify-center mb-6">
              <div className="text-center text-gray-500">
                <MapPin className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Interactive map will appear here</p>
                <p className="text-xs">during location analysis</p>
              </div>
            </div>

            {/* Mock Data Points */}
            <div className="space-y-4">
              <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-blue-900">Market Density</span>
                  <span className="text-sm text-blue-700">Medium</span>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-green-900">Opportunity Score</span>
                  <span className="text-sm text-green-700">High</span>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-purple-900">Competition Level</span>
                  <span className="text-sm text-purple-700">Moderate</span>
                </div>
              </div>
            </div>

            {/* Analysis Tools */}
            <div className="mt-6">
              <h3 className="text-sm font-semibold mb-3">Analysis Tools</h3>
              <div className="grid grid-cols-2 gap-2">
                <button className="p-2 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors">
                  Demographics
                </button>
                <button className="p-2 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors">
                  Traffic Heat
                </button>
                <button className="p-2 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors">
                  Competitors
                </button>
                <button className="p-2 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors">
                  Pricing
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
