"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { ArrowRight, MapPin, TrendingUp, DollarSign, FileText, Users, Coffee, UtensilsCrossed } from "lucide-react";

export default function LandingPage() {
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleStartResearch = () => {
    if (!inputValue.trim()) return;
    
    setIsLoading(true);
    
    // Store the initial research query
    localStorage.setItem('market_research_query', inputValue);
    localStorage.setItem('research_timestamp', new Date().toISOString());
    
    // Navigate to chat interface
    router.push('/chat');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleStartResearch();
    }
  };

  const exampleQueries = [
    "I want to open a specialty coffee shop in downtown Seattle",
    "Planning a casual dining restaurant in Austin, Texas",
    "Considering a fast-casual concept near university campus", 
    "Opening an artisan bakery café in suburban Denver"
  ];

  const features = [
    {
      icon: <MapPin className="w-8 h-8" />,
      title: "Location Analysis",
      description: "Demographics, foot traffic, and commercial environment assessment"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Competition Research", 
      description: "Comprehensive competitor analysis and market gap identification"
    },
    {
      icon: <DollarSign className="w-8 h-8" />,
      title: "Financial Projections",
      description: "Detailed startup costs, revenue forecasts, and profitability analysis"
    },
    {
      icon: <TrendingUp className="w-8 h-8" />,
      title: "Market Insights",
      description: "Industry trends, target audience analysis, and strategic recommendations"
    },
    {
      icon: <FileText className="w-8 h-8" />,
      title: "Professional Reports",
      description: "Comprehensive feasibility studies and market research documentation"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-2 rounded-xl">
                <TrendingUp className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">MarketScope AI</h1>
                <p className="text-sm text-gray-600">Restaurant & Cafe Market Research</p>
              </div>
            </div>
            
            <nav className="hidden md:flex items-center space-x-6">
              <a href="#features" className="text-gray-600 hover:text-blue-600 transition-colors">Features</a>
              <a href="#how-it-works" className="text-gray-600 hover:text-blue-600 transition-colors">How it Works</a>
              <Link href="/history" className="text-gray-600 hover:text-blue-600 transition-colors">Research History</Link>
              <Link href="/chat" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Start Research
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center max-w-4xl mx-auto mb-16">
          <div className="flex justify-center mb-6">
            <div className="flex items-center space-x-4">
              <Coffee className="w-12 h-12 text-blue-600" />
              <UtensilsCrossed className="w-12 h-12 text-indigo-600" />
            </div>
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            Professional Market Research for
            <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent"> Restaurant Entrepreneurs</span>
          </h1>
          
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            Get comprehensive market analysis, competitor research, and financial projections 
            powered by AI. Make informed decisions before investing in your restaurant or cafe.
          </p>

          {/* Main CTA Input */}
          <div className="bg-white rounded-2xl shadow-xl p-8 max-w-3xl mx-auto mb-12">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6">
              Describe your restaurant or cafe concept
            </h2>
            
            <div className="space-y-4">
              <div className="relative">
                <textarea
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="e.g., I want to open a specialty coffee shop in downtown Seattle with a focus on single-origin beans and co-working space..."
                  className="w-full p-4 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:outline-none resize-none h-32 text-lg"
                  disabled={isLoading}
                />
              </div>
              
              <button
                onClick={handleStartResearch}
                disabled={!inputValue.trim() || isLoading}
                className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-8 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 flex items-center justify-center space-x-3"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                    <span>Initializing Research...</span>
                  </>
                ) : (
                  <>
                    <span>Start Market Research</span>
                    <ArrowRight className="w-6 h-6" />
                  </>
                )}
              </button>
            </div>
            
            {/* Example queries */}
            <div className="mt-6">
              <p className="text-sm text-gray-500 mb-3">💡 Try these examples:</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {exampleQueries.map((query, index) => (
                  <button
                    key={index}
                    onClick={() => setInputValue(query)}
                    className="text-left p-3 bg-gray-50 hover:bg-blue-50 rounded-lg text-sm text-gray-700 hover:text-blue-700 transition-colors border border-transparent hover:border-blue-200"
                  >
                    &ldquo;{query}&rdquo;
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <section id="features" className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Complete Market Research Suite
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.slice(0, 3).map((feature, index) => (
              <div key={index} className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow">
                <div className="bg-gradient-to-r from-blue-100 to-indigo-100 p-3 rounded-xl w-fit mb-4">
                  <div className="text-blue-600">{feature.icon}</div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8 max-w-4xl mx-auto">
            {features.slice(3).map((feature, index) => (
              <div key={index + 3} className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow">
                <div className="bg-gradient-to-r from-blue-100 to-indigo-100 p-3 rounded-xl w-fit mb-4">
                  <div className="text-blue-600">{feature.icon}</div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* How it Works */}
        <section id="how-it-works" className="bg-white rounded-3xl p-12 shadow-xl">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            How MarketScope AI Works
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold mx-auto mb-4">1</div>
              <h3 className="text-xl font-semibold mb-3">Describe Your Concept</h3>
              <p className="text-gray-600">Tell us about your restaurant or cafe idea, target location, and business goals</p>
            </div>
            
            <div className="text-center">
              <div className="bg-indigo-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold mx-auto mb-4">2</div>
              <h3 className="text-xl font-semibold mb-3">AI Research & Analysis</h3>
              <p className="text-gray-600">Our AI analyzes market data, competition, demographics, and financial projections</p>
            </div>
            
            <div className="text-center">
              <div className="bg-purple-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold mx-auto mb-4">3</div>
              <h3 className="text-xl font-semibold mb-3">Get Professional Report</h3>
              <p className="text-gray-600">Receive comprehensive market research with actionable insights and recommendations</p>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white mt-20 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex justify-center items-center space-x-3 mb-4">
              <TrendingUp className="w-8 h-8" />
              <h3 className="text-2xl font-bold">MarketScope AI</h3>
            </div>
            <p className="text-gray-400 mb-6">Professional Market Research for Restaurant Entrepreneurs</p>
            <p className="text-sm text-gray-500">© 2025 MarketScope AI. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
