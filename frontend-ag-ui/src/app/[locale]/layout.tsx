import type { Metada<PERSON> } from "next";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { CopilotKit } from "@copilotkit/react-core";
import "../globals.css";
import "@copilotkit/react-ui/styles.css";

export const metadata: Metadata = {
  title: "BiteBase Intelligence - AI-Powered Restaurant Market Research",
  description: "Discover prime locations, analyze competitors, and make data-driven decisions for your restaurant business with our advanced AI analytics platform.",
};

export default async function LocaleLayout({
  children,
  params: { locale }
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body className="antialiased">
        <NextIntlClientProvider messages={messages}>
          <CopilotKit 
            runtimeUrl="/api/copilotkit" 
            publicApiKey="ck_pub_4bae12d076311a78139ec12d2215c973"
          >
            {children}
          </CopilotKit>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
