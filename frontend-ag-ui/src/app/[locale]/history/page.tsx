"use client";

import { AppLayout } from "@/components/layout";
import { StatusBadge, DataCard } from "@/components/ui-components";
import { Calendar, MapPin, Users, DollarSign, FileText, Eye, Download, Trash2 } from "lucide-react";
import Link from "next/link";

// Mock data - in real app, this would come from an API
const mockReports = [
  {
    id: "1",
    title: "Coffee Shop - Downtown Seattle",
    businessType: "Specialty Coffee Shop",
    location: "Pike Place Market Area, Seattle, WA",
    status: "completed",
    createdAt: "2024-01-15",
    budget: 150000,
    score: 8.5,
    insights: [
      "High foot traffic from tourists",
      "Strong coffee culture in area",
      "Premium pricing acceptable"
    ],
    keyMetrics: {
      competitorDensity: "Medium",
      marketOpportunity: "High",
      breakEvenMonths: 8
    }
  },
  {
    id: "2", 
    title: "Fast Casual Restaurant - Austin",
    businessType: "Mexican Fast Casual",
    location: "South Austin, TX",
    status: "in-progress",
    createdAt: "2024-01-12",
    budget: 300000,
    score: 7.2,
    insights: [
      "Growing residential area",
      "Limited Mexican food options",
      "Family-friendly demographics"
    ],
    keyMetrics: {
      competitorDensity: "Low",
      marketOpportunity: "High",
      breakEvenMonths: 12
    }
  },
  {
    id: "3",
    title: "Italian Bistro - Brooklyn",
    businessType: "Italian Restaurant",
    location: "Park Slope, Brooklyn, NY", 
    status: "draft",
    createdAt: "2024-01-10",
    budget: 250000,
    score: 6.8,
    insights: [
      "High competition from established Italian restaurants",
      "Affluent neighborhood",
      "Strong dinner market"
    ],
    keyMetrics: {
      competitorDensity: "High",
      marketOpportunity: "Medium",
      breakEvenMonths: 15
    }
  }
];

export default function HistoryPage() {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-700 bg-green-100';
      case 'in-progress': return 'text-blue-700 bg-blue-100';
      case 'draft': return 'text-gray-700 bg-gray-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-600';
    if (score >= 6) return 'text-yellow-600'; 
    return 'text-red-600';
  };

  return (
    <AppLayout 
      title="Research History"
      subtitle="View and manage your market research reports"
    >
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <DataCard
            title="Total Reports"
            value={mockReports.length}
            color="blue"
          />
          <DataCard
            title="Completed"
            value={mockReports.filter(r => r.status === 'completed').length}
            color="green"
          />
          <DataCard
            title="In Progress"
            value={mockReports.filter(r => r.status === 'in-progress').length}
            color="orange"
          />
          <DataCard
            title="Avg. Score"
            value={(mockReports.reduce((acc, r) => acc + r.score, 0) / mockReports.length).toFixed(1)}
            color="purple"
          />
        </div>

        {/* Action Bar */}
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center space-x-4">
            <h2 className="text-lg font-semibold text-gray-900">Your Reports</h2>
            <select className="border border-gray-300 rounded-md px-3 py-1 text-sm">
              <option>All Status</option>
              <option>Completed</option>
              <option>In Progress</option>
              <option>Draft</option>
            </select>
            <select className="border border-gray-300 rounded-md px-3 py-1 text-sm">
              <option>Sort by Date</option>
              <option>Sort by Score</option>
              <option>Sort by Budget</option>
            </select>
          </div>
          <Link 
            href="/chat"
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            New Research
          </Link>
        </div>

        {/* Reports Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {mockReports.map((report) => (
            <div key={report.id} className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              {/* Header */}
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="font-semibold text-gray-900 text-lg leading-tight">
                    {report.title}
                  </h3>
                  <div className="flex items-center ml-3">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(report.status)}`}>
                      {report.status}
                    </span>
                  </div>
                </div>
                
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-2 text-gray-400" />
                    {report.location}
                  </div>
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                    {new Date(report.createdAt).toLocaleDateString()}
                  </div>
                  <div className="flex items-center">
                    <DollarSign className="w-4 h-4 mr-2 text-gray-400" />
                    ${report.budget.toLocaleString()} budget
                  </div>
                </div>
              </div>

              {/* Score & Metrics */}
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-sm font-medium text-gray-700">Opportunity Score</span>
                  <span className={`text-2xl font-bold ${getScoreColor(report.score)}`}>
                    {report.score}/10
                  </span>
                </div>
                
                <div className="grid grid-cols-3 gap-3 text-xs">
                  <div className="text-center p-2 bg-gray-50 rounded">
                    <div className="font-medium text-gray-900">Competition</div>
                    <div className="text-gray-600 mt-1">{report.keyMetrics.competitorDensity}</div>
                  </div>
                  <div className="text-center p-2 bg-gray-50 rounded">
                    <div className="font-medium text-gray-900">Opportunity</div>
                    <div className="text-gray-600 mt-1">{report.keyMetrics.marketOpportunity}</div>
                  </div>
                  <div className="text-center p-2 bg-gray-50 rounded">
                    <div className="font-medium text-gray-900">Break-even</div>
                    <div className="text-gray-600 mt-1">{report.keyMetrics.breakEvenMonths}mo</div>
                  </div>
                </div>
              </div>

              {/* Key Insights Preview */}
              <div className="p-6 border-b border-gray-100">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Key Insights</h4>
                <ul className="space-y-2">
                  {report.insights.slice(0, 2).map((insight, index) => (
                    <li key={index} className="text-sm text-gray-600 flex items-start">
                      <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-2 flex-shrink-0"></div>
                      {insight}
                    </li>
                  ))}
                  {report.insights.length > 2 && (
                    <li className="text-xs text-gray-500">+{report.insights.length - 2} more insights...</li>
                  )}
                </ul>
              </div>

              {/* Actions */}
              <div className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Link 
                      href={`/report/${report.id}`}
                      className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      View
                    </Link>
                    {report.status === 'completed' && (
                      <button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50 transition-colors">
                        <Download className="w-4 h-4 mr-1" />
                        Export
                      </button>
                    )}
                  </div>
                  
                  <button className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors">
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State (if no reports) */}
        {mockReports.length === 0 && (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No reports yet</h3>
            <p className="text-gray-600 mb-6">Start your first market research project to see reports here.</p>
            <Link 
              href="/chat"
              className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Start Research Project
            </Link>
          </div>
        )}
      </div>
    </AppLayout>
  );
}
