import {
  CopilotRuntime,
  OpenAIAdapter,
  copilotRuntimeNextJSAppRouterEndpoint,
} from "@copilotkit/runtime";
import { NextRequest } from "next/server";
import OpenAI from "openai";

// OpenAI-compatible configuration - works with multiple providers
const config = {
  apiKey: process.env.OPENAI_API_KEY || "your_api_key_here",
  baseURL: process.env.OPENAI_BASE_URL || "https://api.openai.com/v1",
  model: process.env.OPENAI_MODEL || "gpt-4o-mini",
};

// Create OpenAI client with flexible endpoint support
const openai = new OpenAI({
  baseURL: config.baseURL,
  apiKey: config.apiKey,
});

// Create service adapter using configured OpenAI client
const serviceAdapter = new OpenAIAdapter({
  openai,
  model: config.model,
});

// Create runtime for AI processing
const runtime = new CopilotRuntime();

export const POST = async (req: NextRequest) => {
  const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
    runtime, 
    serviceAdapter,
    endpoint: "/api/copilotkit",
  });
 
  return handleRequest(req);
};