"use client";

import { AppLayout } from "@/components/layout";
import { StatusBadge, DataCard, InsightCard, ProgressBar } from "@/components/ui-components";
import { MapPin, Calendar, DollarSign, Users, TrendingUp, Download, Share2, Edit, BarChart3 } from "lucide-react";
import { use } from "react";

// Mock detailed report data - in real app, this would come from an API
const mockDetailedReport = {
  id: "1",
  title: "Coffee Shop Market Analysis",
  businessType: "Specialty Coffee Shop", 
  location: "Pike Place Market Area, Seattle, WA",
  status: "completed",
  createdAt: "2024-01-15",
  completedAt: "2024-01-15",
  budget: 150000,
  targetAudience: "Tourists and Local Professionals",
  
  // Executive Summary
  executiveSummary: {
    opportunityScore: 8.5,
    recommendation: "Highly Recommended",
    riskLevel: "Medium",
    keyFindings: [
      "High foot traffic from Pike Place Market tourism",
      "Strong local coffee culture with premium pricing acceptance",
      "Limited direct competition in specialty coffee segment",
      "Strong morning and lunch rush potential"
    ]
  },
  
  // Location Analysis
  locationAnalysis: {
    score: 9.2,
    demographics: {
      population: 47000,
      medianIncome: 85000,
      ageGroups: {
        "18-34": 35,
        "35-54": 40,
        "55+": 25
      }
    },
    footTraffic: {
      daily: 15000,
      peak: "8-10 AM, 12-2 PM",
      seasonal: "Summer peak (June-August)"
    },
    accessibility: {
      publicTransport: "Excellent",
      parking: "Limited",
      walkability: "High"
    }
  },
  
  // Competition Analysis
  competitionAnalysis: {
    score: 7.8,
    directCompetitors: [
      {
        name: "Starbucks Pike Place",
        distance: "0.2 miles",
        type: "Chain Coffee",
        rating: 4.1,
        priceRange: "$$",
        strengths: ["Brand recognition", "Location"],
        weaknesses: ["Generic experience", "Limited seating"]
      },
      {
        name: "Cherry Street Coffee House",
        distance: "0.3 miles", 
        type: "Local Coffee Shop",
        rating: 4.6,
        priceRange: "$$$",
        strengths: ["Local brand", "Quality coffee"],
        weaknesses: ["Higher prices", "Limited hours"]
      }
    ],
    marketGaps: [
      "Specialty single-origin focus",
      "Tourist-friendly experience",
      "Extended evening hours",
      "Local pastry partnerships"
    ]
  },
  
  // Financial Projections
  financialProjections: {
    startupCosts: {
      equipment: 45000,
      renovation: 35000,
      inventory: 15000,
      permits: 8000,
      marketing: 12000,
      workingCapital: 35000,
      total: 150000
    },
    monthlyProjections: {
      revenue: [8000, 12000, 18000, 25000, 30000, 35000],
      expenses: [22000, 20000, 19000, 18000, 17500, 17000],
      profit: [-14000, -8000, -1000, 7000, 12500, 18000]
    },
    breakEvenMonth: 3,
    yearOneRevenue: 320000,
    yearOneProfit: 85000
  },
  
  // Strategic Recommendations
  recommendations: [
    {
      category: "Location Strategy",
      priority: "High",
      recommendation: "Secure storefront with street-level visibility and tourist foot traffic",
      impact: "High revenue potential from tourist market"
    },
    {
      category: "Product Positioning", 
      priority: "High",
      recommendation: "Focus on specialty single-origin coffee with local partnerships",
      impact: "Differentiation from chain competitors"
    },
    {
      category: "Operating Hours",
      priority: "Medium", 
      recommendation: "Extended hours (6 AM - 8 PM) to capture evening market",
      impact: "Additional revenue stream in underserved time slot"
    },
    {
      category: "Marketing Strategy",
      priority: "Medium",
      recommendation: "Partner with local hotels and tourist information centers",
      impact: "Steady tourist customer referrals"
    }
  ],
  
  // Risk Factors
  risks: [
    {
      factor: "Seasonal Tourism",
      level: "Medium",
      impact: "Revenue fluctuation based on tourist seasons",
      mitigation: "Build local customer base and adjust staffing seasonally"
    },
    {
      factor: "High Rent Costs",
      level: "High", 
      impact: "Prime location comes with premium rent expenses",
      mitigation: "Negotiate longer lease with favorable terms"
    },
    {
      factor: "Competition Response",
      level: "Low",
      impact: "Existing competitors may adjust pricing or offerings",
      mitigation: "Focus on unique value proposition and customer experience"
    }
  ]
};

interface ReportPageProps {
  params: Promise<{ id: string }>;
}

export default function ReportPage({ params }: ReportPageProps) {
  const { id } = use(params);
  const report = mockDetailedReport; // In real app: fetch report by ID

  return (
    <AppLayout 
      title={report.title}
      subtitle={`${report.businessType} • ${report.location}`}
      showBackButton={true}
      backButtonHref="/history"
      backButtonText="Back to History"
      headerActions={
        <div className="flex items-center space-x-2">
          <button className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm rounded-md hover:bg-gray-50 transition-colors">
            <Share2 className="w-4 h-4 mr-2" />
            Share
          </button>
          <button className="inline-flex items-center px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors">
            <Download className="w-4 h-4 mr-2" />
            Export PDF
          </button>
        </div>
      }
    >
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Report Header */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <div className="flex items-center text-sm text-gray-600 mb-2">
                <Calendar className="w-4 h-4 mr-2" />
                Analysis Date
              </div>
              <div className="font-semibold">{new Date(report.createdAt).toLocaleDateString()}</div>
            </div>
            <div>
              <div className="flex items-center text-sm text-gray-600 mb-2">
                <DollarSign className="w-4 h-4 mr-2" />
                Investment Budget
              </div>
              <div className="font-semibold">${report.budget.toLocaleString()}</div>
            </div>
            <div>
              <div className="flex items-center text-sm text-gray-600 mb-2">
                <Users className="w-4 h-4 mr-2" />
                Target Audience
              </div>
              <div className="font-semibold">{report.targetAudience}</div>
            </div>
            <div>
              <div className="flex items-center text-sm text-gray-600 mb-2">
                <TrendingUp className="w-4 h-4 mr-2" />
                Opportunity Score
              </div>
              <div className="text-2xl font-bold text-green-600">{report.executiveSummary.opportunityScore}/10</div>
            </div>
          </div>
        </div>

        {/* Executive Summary */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Executive Summary</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <DataCard 
              title="Recommendation"
              value={report.executiveSummary.recommendation}
              color="green"
            />
            <DataCard 
              title="Risk Level" 
              value={report.executiveSummary.riskLevel}
              color="orange"
            />
            <DataCard
              title="Break-Even"
              value={`${report.financialProjections.breakEvenMonth} months`}
              color="blue"
            />
          </div>

          <div>
            <h3 className="font-semibold text-gray-900 mb-3">Key Findings</h3>
            <div className="space-y-3">
              {report.executiveSummary.keyFindings.map((finding, index) => (
                <InsightCard key={index} insight={finding} type="info" />
              ))}
            </div>
          </div>
        </div>

        {/* Location Analysis */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Location Analysis</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Demographics</h3>
              <div className="space-y-4">
                <DataCard
                  title="Population"
                  value={report.locationAnalysis.demographics.population.toLocaleString()}
                  color="blue"
                />
                <DataCard
                  title="Median Income"
                  value={`$${report.locationAnalysis.demographics.medianIncome.toLocaleString()}`}
                  color="green"
                />
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Age Distribution</h4>
                  {Object.entries(report.locationAnalysis.demographics.ageGroups).map(([age, percent]) => (
                    <div key={age} className="mb-2">
                      <ProgressBar 
                        progress={percent as number} 
                        label={age}
                        color="purple"
                        size="sm"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Foot Traffic & Accessibility</h3>
              <div className="space-y-4">
                <DataCard
                  title="Daily Foot Traffic"
                  value={report.locationAnalysis.footTraffic.daily.toLocaleString()}
                  color="orange"
                />
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm font-medium text-gray-700 mb-2">Peak Hours</div>
                  <div className="text-gray-900">{report.locationAnalysis.footTraffic.peak}</div>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-sm font-medium text-gray-700 mb-2">Seasonal Pattern</div>
                  <div className="text-gray-900">{report.locationAnalysis.footTraffic.seasonal}</div>
                </div>
                <div className="grid grid-cols-3 gap-2 text-xs">
                  <div className="text-center p-2 bg-green-50 rounded">
                    <div className="font-medium text-gray-900">Transit</div>
                    <div className="text-green-600 mt-1">{report.locationAnalysis.accessibility.publicTransport}</div>
                  </div>
                  <div className="text-center p-2 bg-yellow-50 rounded">
                    <div className="font-medium text-gray-900">Parking</div>
                    <div className="text-yellow-600 mt-1">{report.locationAnalysis.accessibility.parking}</div>
                  </div>
                  <div className="text-center p-2 bg-blue-50 rounded">
                    <div className="font-medium text-gray-900">Walking</div>
                    <div className="text-blue-600 mt-1">{report.locationAnalysis.accessibility.walkability}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Competition Analysis */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Competition Analysis</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Direct Competitors</h3>
              <div className="space-y-4">
                {report.competitionAnalysis.directCompetitors.map((competitor, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-medium text-gray-900">{competitor.name}</h4>
                        <p className="text-sm text-gray-600">{competitor.type} • {competitor.distance}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">⭐ {competitor.rating}</div>
                        <div className="text-sm text-gray-600">{competitor.priceRange}</div>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="font-medium text-green-600 mb-1">Strengths</div>
                        <ul className="space-y-1">
                          {competitor.strengths.map((strength, i) => (
                            <li key={i} className="text-gray-600">• {strength}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <div className="font-medium text-red-600 mb-1">Weaknesses</div>
                        <ul className="space-y-1">
                          {competitor.weaknesses.map((weakness, i) => (
                            <li key={i} className="text-gray-600">• {weakness}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Market Opportunities</h3>
              <div className="space-y-3">
                {report.competitionAnalysis.marketGaps.map((gap, index) => (
                  <div key={index} className="bg-blue-50 border-l-4 border-blue-400 p-3 rounded">
                    <div className="font-medium text-blue-900">{gap}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Financial Projections */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Financial Projections</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Startup Costs Breakdown</h3>
              <div className="space-y-3">
                {Object.entries(report.financialProjections.startupCosts).map(([category, amount]) => (
                  category !== 'total' && (
                    <div key={category} className="flex justify-between items-center py-2 border-b border-gray-100">
                      <span className="capitalize text-gray-700">{category.replace(/([A-Z])/g, ' $1')}</span>
                      <span className="font-medium">${(amount as number).toLocaleString()}</span>
                    </div>
                  )
                ))}
                <div className="flex justify-between items-center py-2 font-bold text-lg border-t-2 border-gray-200">
                  <span>Total Investment</span>
                  <span>${report.financialProjections.startupCosts.total.toLocaleString()}</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-3">6-Month Revenue Projection</h3>
              <div className="space-y-3">
                {report.financialProjections.monthlyProjections.revenue.map((revenue, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="text-gray-700">Month {index + 1}</span>
                    <div className="text-right">
                      <div className="font-medium">${revenue.toLocaleString()}</div>
                      <div className={`text-sm ${report.financialProjections.monthlyProjections.profit[index] >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {report.financialProjections.monthlyProjections.profit[index] >= 0 ? '+' : ''}${report.financialProjections.monthlyProjections.profit[index].toLocaleString()} profit
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 p-4 bg-green-50 rounded-lg">
                <div className="text-sm font-medium text-green-800">Year 1 Projections</div>
                <div className="mt-2 space-y-1">
                  <div className="flex justify-between">
                    <span className="text-green-700">Revenue:</span>
                    <span className="font-medium">${report.financialProjections.yearOneRevenue.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-green-700">Profit:</span>
                    <span className="font-medium">${report.financialProjections.yearOneProfit.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Strategic Recommendations */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Strategic Recommendations</h2>
          
          <div className="space-y-4">
            {report.recommendations.map((rec, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-900">{rec.category}</h3>
                  <StatusBadge status={rec.priority} variant="pill" />
                </div>
                <p className="text-gray-700 mb-2">{rec.recommendation}</p>
                <div className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
                  <strong>Impact:</strong> {rec.impact}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Risk Assessment */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Risk Assessment</h2>
          
          <div className="space-y-4">
            {report.risks.map((risk, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-900">{risk.factor}</h3>
                  <StatusBadge status={risk.level} variant="pill" />
                </div>
                <p className="text-gray-700 mb-2"><strong>Impact:</strong> {risk.impact}</p>
                <div className="text-sm text-green-700 bg-green-50 p-2 rounded">
                  <strong>Mitigation:</strong> {risk.mitigation}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
