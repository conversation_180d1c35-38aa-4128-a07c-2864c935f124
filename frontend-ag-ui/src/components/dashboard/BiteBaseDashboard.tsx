'use client';

import React from 'react';
import { useCopilotAction } from '@copilotkit/react-core';
import { TrendingUp, Users, DollarSign, Clock } from 'lucide-react';

interface AgentState {
  selectedRestaurant?: string;
  currentView?: string;
  metrics?: {
    revenue: string;
    orders: string;
    customers: string;
    avgOrderTime: string;
  };
  insights?: string[];
  lastReportGenerated?: string;
}

interface DashboardProps {
  agentState: AgentState;
  onStateChange: (state: AgentState) => void;
}

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  icon: React.ReactNode;
  trend: 'up' | 'down' | 'neutral';
}

function MetricCard({ title, value, change, icon, trend }: MetricCardProps) {
  const trendColor = trend === 'up' ? 'text-green-600' : trend === 'down' ? 'text-red-600' : 'text-gray-600';
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-3xl font-bold text-gray-900">{value}</p>
          <p className={`text-sm ${trendColor}`}>{change}</p>
        </div>
        <div className="text-gray-400">
          {icon}
        </div>
      </div>
    </div>
  );
}

export default function BiteBaseDashboard({ agentState, onStateChange }: DashboardProps) {
  // AG-UI Integration - Restaurant Selection
  useCopilotAction({
    name: "selectRestaurant",
    description: "Select a restaurant to view analytics for",
    parameters: [{
      name: "restaurantName",
      description: "Name of the restaurant to analyze",
      required: true,
    }],
    handler: ({ restaurantName }) => {
      onStateChange({
        ...agentState,
        selectedRestaurant: restaurantName,
        metrics: generateMockMetrics(restaurantName),
      });
    },
  });

  // AG-UI Integration - View Switching
  useCopilotAction({
    name: "switchDashboardView",
    description: "Switch between different dashboard views",
    parameters: [{
      name: "view",
      description: "Dashboard view to switch to: overview, sales, customers, inventory",
      required: true,
    }],
    handler: ({ view }) => {
      onStateChange({
        ...agentState,
        currentView: view,
      });
    },
  });

  // AG-UI Integration - Generate Report
  useCopilotAction({
    name: "generateInsightsReport",
    description: "Generate AI-powered insights report for the restaurant",
    parameters: [{
      name: "timeframe",
      description: "Time period for the report: today, week, month, quarter",
      required: true,
    }],
    handler: ({ timeframe }) => {
      const insights = generateInsights(agentState.selectedRestaurant || "Demo Restaurant", timeframe);
      onStateChange({
        ...agentState,
        insights: insights,
        lastReportGenerated: new Date().toISOString(),
      });
    },
  });

  const currentRestaurant = agentState?.selectedRestaurant || "Demo Restaurant";
  const currentView = agentState?.currentView || "overview";
  const metrics = agentState?.metrics || getDefaultMetrics();
  const insights = agentState?.insights || [];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          BiteBase Intelligence
        </h1>
        <p className="text-gray-600">
          Analyzing data for: <span className="font-semibold text-blue-600">{currentRestaurant}</span>
        </p>
        <p className="text-sm text-gray-500 mt-1">
          Current view: {currentView} • Ask the AI assistant to switch views or generate reports
        </p>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <MetricCard
          title="Revenue Today"
          value={metrics.revenue}
          change="+12.5% vs yesterday"
          icon={<DollarSign size={32} />}
          trend="up"
        />
        <MetricCard
          title="Orders"
          value={metrics.orders}
          change="****% vs yesterday"
          icon={<TrendingUp size={32} />}
          trend="up"
        />
        <MetricCard
          title="Customers"
          value={metrics.customers}
          change="+15.3% vs yesterday"
          icon={<Users size={32} />}
          trend="up"
        />
        <MetricCard
          title="Avg Order Time"
          value={metrics.avgOrderTime}
          change="-2.1 min vs yesterday"
          icon={<Clock size={32} />}
          trend="up"
        />
      </div>

      {/* AI Insights Section */}
      {insights.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            🤖 AI-Generated Insights
          </h3>
          <div className="space-y-3">
            {insights.map((insight: string, index: number) => (
              <div key={index} className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                <p className="text-gray-700">{insight}</p>
              </div>
            ))}
          </div>
          <p className="text-xs text-gray-500 mt-4">
            Generated: {agentState.lastReportGenerated ? new Date(agentState.lastReportGenerated).toLocaleString() : 'Never'}
          </p>
        </div>
      )}

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button 
            onClick={() => onStateChange({ ...agentState, currentView: 'sales' })}
            className="p-4 text-left bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-colors"
          >
            <TrendingUp className="mb-2" size={24} />
            <h4 className="font-semibold">Sales Analytics</h4>
            <p className="text-sm opacity-90">View detailed sales data</p>
          </button>
          
          <button 
            onClick={() => onStateChange({ ...agentState, currentView: 'customers' })}
            className="p-4 text-left bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-colors"
          >
            <Users className="mb-2" size={24} />
            <h4 className="font-semibold">Customer Insights</h4>
            <p className="text-sm opacity-90">Analyze customer behavior</p>
          </button>
          
          <button 
            onClick={() => onStateChange({ ...agentState, currentView: 'inventory' })}
            className="p-4 text-left bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 transition-colors"
          >
            <Clock className="mb-2" size={24} />
            <h4 className="font-semibold">Inventory Status</h4>
            <p className="text-sm opacity-90">Check stock levels</p>
          </button>
        </div>

        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-600 mb-2">
            💡 <strong>Try asking the AI:</strong>
          </p>
          <ul className="text-sm text-gray-500 space-y-1">
            <li>• &ldquo;Switch to sales analytics view&rdquo;</li>
            <li>• &ldquo;Generate insights report for this week&rdquo;</li>
            <li>• &ldquo;Select Pizza Palace restaurant&rdquo;</li>
            <li>• &ldquo;What are the key metrics for today?&rdquo;</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

// Helper functions for mock data
function generateMockMetrics(restaurantName: string) {
  const multiplier = restaurantName.length / 10; // Simple way to vary data by restaurant
  return {
    revenue: `$${(2400 * multiplier).toFixed(0)}`,
    orders: `${Math.floor(89 * multiplier)}`,
    customers: `${Math.floor(156 * multiplier)}`,
    avgOrderTime: `${(18.5 / multiplier).toFixed(1)} min`,
  };
}

function getDefaultMetrics() {
  return {
    revenue: "$2,400",
    orders: "89",
    customers: "156",
    avgOrderTime: "18.5 min",
  };
}

function generateInsights(restaurantName: string, timeframe: string): string[] {
  const insights = [
    `Peak hours for ${restaurantName} during ${timeframe}: 12PM-2PM and 6PM-8PM show highest volume`,
    `Most popular items this ${timeframe}: Margherita Pizza and Caesar Salad drive 34% of revenue`,
    `Customer satisfaction trend: 4.2/5 average rating with delivery time being the top concern`,
    `Revenue opportunity: Weekend dinner service shows 23% growth potential based on current capacity`,
  ];
  
  return insights.slice(0, 3); // Return 3 insights
}
