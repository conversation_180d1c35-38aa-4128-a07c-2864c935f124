import React from 'react';

interface ProgressBarProps {
  progress: number;
  label?: string;
  color?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function ProgressBar({ progress, label, color = "blue", size = "md" }: ProgressBarProps) {
  const heights = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };

  const colors = {
    blue: 'bg-blue-600',
    green: 'bg-green-600',
    purple: 'bg-purple-600',
    red: 'bg-red-600'
  };

  return (
    <div className="w-full">
      {label && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">{label}</span>
          <span className="text-sm text-gray-500">{progress}%</span>
        </div>
      )}
      <div className={`w-full bg-gray-200 rounded-full ${heights[size]}`}>
        <div
          className={`${colors[color]} ${heights[size]} rounded-full transition-all duration-300 ease-out`}
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
    </div>
  );
}

interface InsightCardProps {
  insight: string;
  type?: 'info' | 'success' | 'warning' | 'error';
  timestamp?: string;
}

export function InsightCard({ insight, type = 'info', timestamp }: InsightCardProps) {
  const typeStyles = {
    info: 'bg-blue-50 border-blue-200 text-blue-900',
    success: 'bg-green-50 border-green-200 text-green-900',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-900',
    error: 'bg-red-50 border-red-200 text-red-900'
  };

  return (
    <div className={`p-3 rounded-lg border ${typeStyles[type]} text-sm`}>
      <p>{insight}</p>
      {timestamp && (
        <p className="text-xs opacity-70 mt-1">{timestamp}</p>
      )}
    </div>
  );
}

interface StatusBadgeProps {
  status: string;
  variant?: 'dot' | 'pill';
}

export function StatusBadge({ status, variant = 'dot' }: StatusBadgeProps) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'consultation':
        return 'text-blue-700 bg-blue-100';
      case 'location-analysis':
        return 'text-purple-700 bg-purple-100';
      case 'competitor-research':
        return 'text-orange-700 bg-orange-100';
      case 'financial-modeling':
        return 'text-green-700 bg-green-100';
      case 'report-generation':
        return 'text-red-700 bg-red-100';
      case 'done':
        return 'text-emerald-700 bg-emerald-100';
      default:
        return 'text-gray-700 bg-gray-100';
    }
  };

  const displayStatus = status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());

  if (variant === 'pill') {
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
        {displayStatus}
      </span>
    );
  }

  return (
    <div className="flex items-center text-sm">
      <div className={`w-2 h-2 rounded-full mr-2 ${getStatusColor(status).replace('text-', 'bg-').replace('bg-', 'bg-').split(' ')[1]}`}></div>
      <span className="font-medium capitalize">{displayStatus}</span>
    </div>
  );
}

interface DataCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: 'up' | 'down' | 'neutral';
  color?: string;
}

export function DataCard({ title, value, subtitle, trend, color = 'blue' }: DataCardProps) {
  const colorClasses = {
    blue: 'from-blue-50 to-blue-100 text-blue-900',
    green: 'from-green-50 to-green-100 text-green-900',
    purple: 'from-purple-50 to-purple-100 text-purple-900',
    red: 'from-red-50 to-red-100 text-red-900',
    orange: 'from-orange-50 to-orange-100 text-orange-900'
  };

  const trendIcons = {
    up: '↗',
    down: '↘',
    neutral: '→'
  };

  return (
    <div className={`bg-gradient-to-r ${colorClasses[color]} p-4 rounded-lg`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium opacity-80">{title}</p>
          <p className="text-lg font-bold">{value}</p>
          {subtitle && (
            <p className="text-xs opacity-70">{subtitle}</p>
          )}
        </div>
        {trend && (
          <div className="text-xl opacity-50">
            {trendIcons[trend]}
          </div>
        )}
      </div>
    </div>
  );
}
