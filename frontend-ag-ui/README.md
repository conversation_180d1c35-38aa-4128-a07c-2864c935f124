# BiteBase Intelligence - Agentic AI Platform

BiteBase Intelligence is a comprehensive agentic AI platform for restaurant market research, featuring 12 specialized AI agents with 24 tools for location analysis, competitive intelligence, financial modeling, and business feasibility studies.

## 🚀 Quick Setup

### Prerequisites
- Node.js 18+
- npm (recommended)
- OpenAI API Key or Anthropic API Key

### Easy Installation

**Option 1: Automated Setup (Recommended)**
```bash
# Clone and setup
git clone <repository>
cd frontend-ag-ui

# Run automated setup script
./setup.sh    # Linux/Mac
setup.bat     # Windows
```

**Option 2: Manual Installation**
```bash
# Install dependencies
npm install
cd agent && npm install && cd ..

# Create environment file
cp .env.example .env.local  # Edit with your API keys

# Start development servers
npm run dev
```

# Using bun
bun install
```

2. Install dependencies for the LangGraph agent:
```bash
cd agent
```
```bash
# Using pnpm (recommended)
pnpm install 

# Using npm
npm run install

# Using yarn
yarn install

# Using bun
bun run install
```

3. Set up your OpenAI API key:
```bash
cd agent
echo "OPENAI_API_KEY=your-openai-api-key-here" > .env
```

4. Start the development server:
```bash
# Using pnpm (recommended)
pnpm dev

# Using npm
npm run dev

# Using yarn
yarn dev

# Using bun
bun run dev
```

This will start both the UI and agent servers concurrently.

## Available Scripts
The following scripts can also be run using your preferred package manager:
- `dev` - Starts both UI and agent servers in development mode
- `dev:studio` - Starts the UI and agent with LangGraph Studio
- `dev:debug` - Starts development servers with debug logging enabled
- `dev:ui` - Starts only the Next.js UI server
- `dev:agent` - Starts only the LangGraph agent server
- `dev:agent:studio` - Starts the LangGraph agent server with LangGraph Studio
- `build` - Builds the Next.js application for production
- `start` - Starts the production server
- `lint` - Runs ESLint for code linting
- `install:agent` - Installs Python dependencies for the agent

## Documentation

The main UI component is in `src/app/page.tsx`. You can:
- Modify the theme colors and styling
- Add new frontend actions
- Utilize shared-state
- Customize your user-interface for interactin with LangGraph

## 📚 Documentation

- [CopilotKit Documentation](https://docs.copilotkit.ai) - Explore CopilotKit's capabilities
- [LangGraph Documentation](https://langchain-ai.github.io/langgraph/) - Learn more about LangGraph and its features
- [Next.js Documentation](https://nextjs.org/docs) - Learn about Next.js features and API

## Contributing

Feel free to submit issues and enhancement requests! This starter is designed to be easily extensible.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Troubleshooting

### Agent Connection Issues
If you see "I'm having trouble connecting to my tools", make sure:
1. The LangGraph agent is running on port 8000
2. Your OpenAI API key is set correctly
3. Both servers started successfully
