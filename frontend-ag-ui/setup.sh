#!/bin/bash

# BiteBase Intelligence - Dependency Installation Script
# This script ensures all dependencies are properly installed

echo "🚀 BiteBase Intelligence - Setting up dependencies..."

# Check Node.js version
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "⚠️  Node.js version $NODE_VERSION detected. Recommended: Node.js 18+"
fi

# Clean previous installs
echo "🧹 Cleaning previous installations..."
rm -rf node_modules package-lock.json yarn.lock
rm -rf agent/node_modules agent/package-lock.json agent/yarn.lock

# Install main dependencies
echo "📦 Installing main project dependencies..."
npm install

# Check if install was successful
if [ $? -ne 0 ]; then
    echo "❌ Failed to install main dependencies"
    exit 1
fi

# Install agent dependencies
echo "📦 Installing agent dependencies..."
cd agent
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install agent dependencies"
    exit 1
fi

cd ..

# Verify critical packages
echo "🔍 Verifying critical packages..."

CRITICAL_PACKAGES=(
    "@copilotkit/react-core"
    "@copilotkit/react-ui" 
    "@copilotkit/runtime"
    "@langchain/langgraph"
    "next"
    "react"
)

for package in "${CRITICAL_PACKAGES[@]}"; do
    if npm list "$package" >/dev/null 2>&1; then
        echo "✅ $package installed"
    else
        echo "❌ $package missing"
    fi
done

# Check environment file
if [ ! -f ".env.local" ]; then
    echo "⚠️  .env.local not found. Creating template..."
    cat > .env.local << EOF
# AI Model Configuration (Choose one)
# OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# LangGraph Configuration
LANGGRAPH_DEPLOYMENT_URL=http://localhost:8124

# Optional: LangSmith Tracing
# LANGSMITH_API_KEY=your_langsmith_api_key_here
EOF
    echo "📝 Please edit .env.local and add your API keys"
fi

echo ""
echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env.local and add your API keys"
echo "2. Run 'npm run dev' to start development servers"
echo "3. Visit http://localhost:3000 to use the application"
echo ""
echo "Available commands:"
echo "  npm run dev          # Start UI + Agent servers"
echo "  npm run dev:studio   # Start with LangGraph Studio"
echo "  npm run dev:all      # Start all servers including Copilot API"
echo ""
