# BiteBase Intelligence - API Configuration

## Required API Keys

To use the BiteBase Intelligence platform, you need to configure API keys for the AI services.

### Option 1: OpenAI (GPT-4) - Recommended for CopilotKit
1. Get your API key from: https://platform.openai.com/account/api-keys
2. Edit `.env.local` and replace:
```
OPENAI_API_KEY=your_openai_api_key_here
```
with:
```
OPENAI_API_KEY=sk-your-actual-openai-key-here
```

### Option 2: Anthropic (Claude 3.5 Sonnet) - Used by LangGraph Agents
1. Get your API key from: https://console.anthropic.com/
2. Edit `.env.local` and replace:
```
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```
with:
```
ANTHROPIC_API_KEY=sk-ant-your-actual-anthropic-key-here
```

### Quick Setup:
1. Copy one of the API keys above into your `.env.local` file
2. Restart the development server: `npm run dev`
3. Test the system at: http://localhost:3000

### Agent Configuration:
Once you have API keys configured, you can re-enable the CoAgent integration by uncommenting the `useCoAgent` lines in `src/app/chat/page.tsx` and commenting out the temporary `useState` version.

### Notes:
- The LangGraph agents (12 specialized restaurant intelligence agents) use Anthropic Claude
- The CopilotKit chat interface uses OpenAI for best compatibility
- You can use either service, but OpenAI is recommended for the chat interface
