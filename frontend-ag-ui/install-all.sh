#!/bin/bash

# BiteBase Intelligence - Complete Installation Script
# This script ensures all dependencies are properly installed

echo "🚀 BiteBase Intelligence - Complete Installation"
echo "=================================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check Node.js version
echo "📋 Checking prerequisites..."
if ! command_exists node; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version must be 18 or higher. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) - OK"
echo "✅ npm $(npm -v) - OK"

# Clean previous installations if requested
if [ "$1" = "--clean" ]; then
    echo "🧹 Cleaning previous installations..."
    rm -rf node_modules package-lock.json
    rm -rf agent/node_modules agent/package-lock.json
    echo "✅ Clean completed"
fi

# Install root dependencies
echo "📦 Installing main dependencies..."
npm install --legacy-peer-deps
if [ $? -ne 0 ]; then
    echo "❌ Failed to install main dependencies"
    exit 1
fi

# Install agent dependencies
echo "📦 Installing agent dependencies..."
cd agent
npm install --legacy-peer-deps
if [ $? -ne 0 ]; then
    echo "❌ Failed to install agent dependencies"
    cd ..
    exit 1
fi
cd ..

# Verify CopilotKit versions
echo "🔍 Verifying CopilotKit versions..."
COPILOT_VERSION=$(npm list @copilotkit/react-core --depth=0 2>/dev/null | grep @copilotkit/react-core | cut -d'@' -f3 || echo "not found")
if [[ "$COPILOT_VERSION" == "1.3.7" ]]; then
    echo "✅ CopilotKit v1.3.7 - OK"
else
    echo "⚠️ CopilotKit version mismatch. Expected 1.3.7, got: $COPILOT_VERSION"
    echo "🔄 Fixing CopilotKit versions..."
    npm install @copilotkit/react-core@1.3.7 @copilotkit/react-ui@1.3.7 @copilotkit/runtime@1.3.7 @copilotkit/react-textarea@1.3.7 @copilotkit/runtime-client-gql@1.3.7 --legacy-peer-deps
fi

# Verify LangChain packages
echo "🔍 Verifying LangChain packages..."
if npm list @langchain/langgraph >/dev/null 2>&1; then
    echo "✅ LangChain packages - OK"
else
    echo "❌ LangChain packages missing"
    exit 1
fi

# Create .env.local template if it doesn't exist
if [ ! -f .env.local ]; then
    echo "📝 Creating .env.local template..."
    cat > .env.local << EOL
# BiteBase Intelligence Environment Variables
# Copy this template and add your actual API keys

# AI Model API Keys
ANTHROPIC_API_KEY=your_anthropic_key_here
OPENAI_API_KEY=your_openai_key_here

# LangGraph Configuration
LANGGRAPH_DEPLOYMENT_URL=http://localhost:8124

# Optional: LangSmith Tracing
LANGSMITH_API_KEY=your_langsmith_key_here
LANGSMITH_TRACING=false

# Optional: GitHub Token for Copilot API
GITHUB_TOKEN=your_github_token_here
EOL
    echo "✅ Created .env.local template - Please add your API keys"
else
    echo "✅ .env.local already exists"
fi

echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "Next steps:"
echo "1. Add your API keys to .env.local"
echo "2. Run: npm run dev"
echo "3. Open: http://localhost:3000"
echo ""
echo "Available commands:"
echo "  npm run dev           - Start development servers"
echo "  npm run dev:ui        - Start only UI server"
echo "  npm run dev:agent     - Start only agent server"
echo "  npm run build         - Build for production"
echo "  npm run reinstall     - Clean and reinstall all deps"
echo ""
