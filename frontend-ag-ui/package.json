{"name": "bitebase-intelligence-ag-ui", "version": "0.1.0", "private": true, "description": "BiteBase Intelligence - Agentic AI Market Research Platform for Restaurant Industry", "author": "BiteBase Intelligence Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"preinstall": "npm run install:agent", "install:agent": "cd agent && npm install", "postinstall": "npm run check:deps", "check:deps": "echo '✅ Checking dependencies...' && npm ls --depth=0 || echo '⚠️ Some dependencies may need attention'", "dev": "concurrently \"npm run dev:ui\" \"npm run dev:agent\" --names ui,agent --prefix-colors blue,green --kill-others", "dev:studio": "concurrently \"npm run dev:ui\" \"npm run dev:agent:studio\" --names ui,agent --prefix-colors blue,green --kill-others", "dev:agent": "cd agent && npx @langchain/langgraph-cli dev --port 8124 --no-browser", "dev:agent:studio": "cd agent && npx @langchain/langgraph-cli dev --port 8124", "copilot:api": "npx copilot-api@latest start --github-token $GITHUB_TOKEN --port 4141", "dev:all": "concurrently \"npm run dev:ui\" \"npm run dev:agent\" \"npm run copilot:api\" --names ui,agent,copilot --prefix-colors blue,green,yellow --kill-others", "dev:ui": "next dev", "build": "next build && npm run build:agent", "build:agent": "cd agent && npm run build || echo 'Agent build skipped'", "start": "next start", "lint": "next lint", "clean": "rm -rf .next node_modules agent/node_modules", "reinstall": "npm run clean && npm install"}, "dependencies": {"@ag-ui/langgraph": "0.0.7", "@copilotkit/react-core": "^1.8.0", "@copilotkit/react-textarea": "^1.8.0", "@copilotkit/react-ui": "^1.8.0", "@copilotkit/runtime": "^1.8.0", "@copilotkit/runtime-client-gql": "^1.8.0", "@langchain/core": "^0.3.14", "@langchain/langgraph": "^0.4.9", "@langchain/openai": "^0.3.14", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.543.0", "next": "15.5.2", "next-intl": "^4.3.7", "react": "19.0.0", "react-dom": "19.0.0", "recharts": "^3.2.0", "tailwind-merge": "^3.3.1", "uuid": "^10.0.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@langchain/langgraph-cli": "0.0.40", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@types/uuid": "^10.0.0", "concurrently": "^9.1.2", "copilot-api": "^0.5.14", "eslint": "^9", "eslint-config-next": "15.5.2", "postcss": "^8", "tailwindcss": "^4", "typescript": "^5"}}