# BiteBase Intelligence Setup Guide

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Git

### Installation

1. **Install Dependencies**
```bash
# Install main project dependencies
npm install

# Install agent dependencies
cd agent && npm install && cd ..
```

2. **Environment Configuration**
Create `.env.local` in the project root:
```bash
# AI Model Configuration (Choose one)
OPENAI_API_KEY=your_openai_api_key
# OR
ANTHROPIC_API_KEY=your_anthropic_api_key

# LangGraph Server
LANGGRAPH_DEPLOYMENT_URL=http://localhost:8124

# Optional: LangSmith Tracing
LANGSMITH_API_KEY=your_langsmith_api_key
```

3. **Start Development Servers**
```bash
# Start both UI and agent servers
npm run dev

# Or start individually:
npm run dev:ui     # Next.js on port 3000
npm run dev:agent  # LangGraph on port 8124
```

### 🏗️ Architecture

- **Frontend**: Next.js 15 with CopilotKit v1.3.7
- **Backend**: LangGraph server with 12 specialized agents
- **AI Integration**: Claude 3.5 Sonnet or OpenAI GPT-4
- **Agent Tools**: 24 specialized restaurant intelligence tools

### 🔧 Available Scripts

```bash
npm run dev          # Start UI + Agent servers
npm run dev:studio   # Start with LangGraph Studio UI
npm run dev:all      # Start UI + Agent + Copilot API servers
npm run build        # Build for production
npm run lint         # Lint code
```

### 🐛 Troubleshooting

#### Common Issues:

1. **"Cannot find package @langchain/langgraph"**
   ```bash
   cd agent && npm install
   ```

2. **"ExperimentalEmptyAdapter is not a constructor"**
   - Fixed: Using OpenAIAdapter instead
   - CopilotKit v1.3.7 compatibility

3. **"Invalid uuid" thread errors**
   - Usually resolves automatically
   - LangGraph server generates proper UUIDs

4. **Port conflicts**
   - Next.js: 3000
   - LangGraph: 8124
   - Copilot API: 4141

#### Health Checks:
```bash
# Check LangGraph server
curl http://localhost:8124/threads -X POST -H "Content-Type: application/json" -d '{"config":{}}'

# Check Next.js
curl http://localhost:3000/api/copilotkit
```

### 🌟 Features

- **Market Research AI**: 12 specialized restaurant intelligence agents
- **Real-time Chat**: Seamless AI conversation interface
- **Location Analysis**: Demographics and market data
- **Competitor Intelligence**: Comprehensive competitive analysis
- **Financial Modeling**: Startup costs and revenue projections
- **Professional Reports**: Business feasibility studies

### 📚 Usage

1. Visit `http://localhost:3000`
2. Enter your restaurant concept on the landing page
3. Chat with the AI for comprehensive market research
4. Get professional insights and reports

The system automatically handles the transition from landing page to chat interface with proper query contextualization.
