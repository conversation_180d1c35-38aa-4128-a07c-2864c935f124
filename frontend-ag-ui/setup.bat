@echo off
REM BiteBase Intelligence - Windows Dependency Installation Script

echo 🚀 BiteBase Intelligence - Setting up dependencies...

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

REM Clean previous installs
echo 🧹 Cleaning previous installations...
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json
if exist yarn.lock del yarn.lock
if exist agent\node_modules rmdir /s /q agent\node_modules
if exist agent\package-lock.json del agent\package-lock.json
if exist agent\yarn.lock del agent\yarn.lock

REM Install main dependencies
echo 📦 Installing main project dependencies...
npm install
if errorlevel 1 (
    echo ❌ Failed to install main dependencies
    pause
    exit /b 1
)

REM Install agent dependencies  
echo 📦 Installing agent dependencies...
cd agent
npm install
if errorlevel 1 (
    echo ❌ Failed to install agent dependencies
    pause
    exit /b 1
)
cd ..

REM Check environment file
if not exist ".env.local" (
    echo ⚠️  .env.local not found. Creating template...
    echo # AI Model Configuration (Choose one^) > .env.local
    echo # OPENAI_API_KEY=your_openai_api_key_here >> .env.local
    echo # ANTHROPIC_API_KEY=your_anthropic_api_key_here >> .env.local
    echo. >> .env.local
    echo # LangGraph Configuration >> .env.local
    echo LANGGRAPH_DEPLOYMENT_URL=http://localhost:8124 >> .env.local
    echo. >> .env.local
    echo # Optional: LangSmith Tracing >> .env.local
    echo # LANGSMITH_API_KEY=your_langsmith_api_key_here >> .env.local
    echo 📝 Please edit .env.local and add your API keys
)

echo.
echo ✅ Setup complete!
echo.
echo Next steps:
echo 1. Edit .env.local and add your API keys
echo 2. Run 'npm run dev' to start development servers
echo 3. Visit http://localhost:3000 to use the application
echo.
echo Available commands:
echo   npm run dev          # Start UI + Agent servers
echo   npm run dev:studio   # Start with LangGraph Studio
echo   npm run dev:all      # Start all servers including Copilot API
echo.
pause
