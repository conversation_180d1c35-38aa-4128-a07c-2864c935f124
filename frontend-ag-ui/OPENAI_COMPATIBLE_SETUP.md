# 🤖 OpenAI-Compatible API Setup Guide

## ✅ System Now Configured For:
- **OpenAI** (GPT-4, GPT-3.5, etc.)
- **OpenRouter** (Access to 150+ models)
- **Together AI** (Fast open-source models)
- **Groq** (Ultra-fast inference)
- **Any OpenAI-compatible endpoint**

## 🚀 Quick Setup Options:

### Option 1: OpenAI (Official, Most Reliable)
1. **Get API Key**: https://platform.openai.com/account/api-keys
2. **Edit `.env.local`**:
```env
OPENAI_API_KEY=sk-proj-your-actual-openai-key-here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini
```

### Option 2: OpenRouter (150+ Models, Good Pricing)
1. **Get API Key**: https://openrouter.ai/keys
2. **Edit `.env.local`**:
```env
OPENAI_API_KEY=sk-or-v1-your-openrouter-key-here
OPENAI_BASE_URL=https://openrouter.ai/api/v1
OPENAI_MODEL=anthropic/claude-3.5-sonnet
```

### Option 3: Together AI (Fast, Cheap Open Source)
1. **Get API Key**: https://api.together.xyz/settings/api-keys
2. **Edit `.env.local`**:
```env
OPENAI_API_KEY=your-together-ai-key-here
OPENAI_BASE_URL=https://api.together.xyz/v1
OPENAI_MODEL=meta-llama/Llama-3-8b-chat-hf
```

### Option 4: Groq (Ultra-Fast Inference)
1. **Get API Key**: https://console.groq.com/keys
2. **Edit `.env.local`**:
```env
OPENAI_API_KEY=gsk_your-groq-key-here
OPENAI_BASE_URL=https://api.groq.com/openai/v1
OPENAI_MODEL=llama-3.1-8b-instant
```

## 🎯 Recommended Models by Provider:

### OpenAI:
- `gpt-4o-mini` - Fast, cheap, good quality ⭐
- `gpt-4o` - Highest quality, more expensive
- `gpt-3.5-turbo` - Budget option

### OpenRouter:
- `anthropic/claude-3.5-sonnet` - Best reasoning ⭐
- `google/gemini-pro` - Good alternative
- `meta-llama/llama-3.1-8b-instruct:free` - Free option

### Together AI:
- `meta-llama/Llama-3-8b-chat-hf` - Fast, reliable ⭐
- `mistralai/Mixtral-8x7B-Instruct-v0.1` - High quality
- `NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO` - Creative

### Groq:
- `llama-3.1-8b-instant` - Lightning fast ⭐
- `mixtral-8x7b-32768` - Large context window
- `gemma-7b-it` - Good balance

## 🔧 Current Configuration:
✅ **Frontend API**: Uses environment variables for flexibility  
✅ **LangGraph Agents**: All 6 agents configured for OpenAI-compatible APIs  
✅ **Fallback Model**: `gpt-4o-mini` if no env vars set  
✅ **Error Handling**: Graceful fallbacks for missing keys  

## 🚀 Launch Commands:
```bash
# Terminal 1: Start LangGraph Server
cd agent && npm run start

# Terminal 2: Start Frontend  
npm run dev
```

## 💡 Cost Comparison (per 1M tokens):
- **OpenAI GPT-4o-mini**: ~$0.60
- **OpenRouter Claude**: ~$3.00
- **Together AI Llama**: ~$0.20
- **Groq Llama**: ~$0.27

## 🔒 Security Note:
Never commit API keys to git! Keep them in `.env.local` which is gitignored.

## 🛠️ Test Your Setup:
After setting your API key, visit: http://localhost:3000/chat
The system should work without any "model not supported" errors.

**Result**: Choose any AI provider you prefer - the system is now fully flexible! 🎉
